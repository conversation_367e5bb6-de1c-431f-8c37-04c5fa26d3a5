package cn.iocoder.yudao.module.system.controller.app.map.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "用户 App - 地图 Response VO")
@Data
@Builder
public class AppMapRespVO {


    @Schema(description = "地点名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京贵宾楼酒店")
    private String name;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市东城区东长安街35号")
    private String address;
    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "39.900099")
    private Double lat;
    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "116.40233")
    private Double lng;
    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市")
    private String province;
    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市")
    private String city;
    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED, example = "东城区")
    private String county;
    @Schema(description = "城市编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "110101")
    private String adcode;


    private String personName;

    private String tel;

    private Integer level;

}
