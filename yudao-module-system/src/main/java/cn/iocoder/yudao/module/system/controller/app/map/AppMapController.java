package cn.iocoder.yudao.module.system.controller.app.map;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.controller.app.map.vo.AppMapRespVO;
import cn.iocoder.yudao.module.system.util.map.TencentMapUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "用户 App - 地图")
@RestController
@RequestMapping("/system/map")
@Validated
public class AppMapController {

    @GetMapping("/search")
    @Operation(summary = "周边搜索接口")
    @Parameter(name = "lat", description = "纬度", required = false)
    @Parameter(name = "lng", description = "经度", required = false)
    @Parameter(name = "keyword", description = "关键字", required = false)
    @Parameter(name = "city", description = "城市", required = false)
    public CommonResult<List<AppMapRespVO>> search(
            @RequestParam(name = "lat", required = false) Double lat,
            @RequestParam(name = "lng", required = false) Double lng,
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "city", required = false) String city
    ) {
        List<AppMapRespVO>list= TencentMapUtils.searchLocations(lat, lng, keyword, 1000,city);
        return success(list);
    }
}
