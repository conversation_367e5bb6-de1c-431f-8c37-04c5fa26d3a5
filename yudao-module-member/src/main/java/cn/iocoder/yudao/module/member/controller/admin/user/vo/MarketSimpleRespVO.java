package cn.iocoder.yudao.module.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 App - 批发市场精简信息 Response VO")
@Data
public class MarketSimpleRespVO {

    @Schema(description = "批发市场编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "122")
    private Long id;

    @Schema(description = "批发市场名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "新发地")
    private String name;
}
