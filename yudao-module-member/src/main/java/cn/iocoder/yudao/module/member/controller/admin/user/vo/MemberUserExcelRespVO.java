package cn.iocoder.yudao.module.member.controller.admin.user.vo;

import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台excel - 会员用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberUserExcelRespVO {

    @ExcelProperty("钱包编号")
    private Long walletId;
    @ExcelProperty("商户名称")
    private String shopName;
    @ExcelProperty("联系人")
    private String contacts;

    @ColumnWidth(20) // 设置更宽的列宽
    @ExcelProperty("手机号")
    private String mobile;

    private Integer balance;

    @ExcelProperty("钱包余额(元)")
    private BigDecimal balanceYuan;
    public BigDecimal getBalanceYuan() {
        if (balance == null) return null;
        return MoneyUtils.fenToYuan(balance);
    }
    private Integer totalRecharge;

    @ExcelProperty("累计充值(元)")
    private BigDecimal totalRechargeYuan;

    public BigDecimal getTotalRechargeYuan() {
        if (totalRecharge == null) return null;
        return MoneyUtils.fenToYuan(totalRecharge);
    }

    private Integer expireRecharge;

    @ExcelProperty("过期充值(元)")
    private BigDecimal expireRechargeYuan;

    public BigDecimal getExpireRechargeYuan() {
        if (expireRecharge == null) return null;
        return MoneyUtils.fenToYuan(expireRecharge);
    }

    //累计消费
    private Integer totalExpense;

    @ExcelProperty("累计消费(元)")
    private BigDecimal totalExpenseYuan;

    public BigDecimal getTotalExpenseYuan() {
        if (totalExpense == null) return null;
        return MoneyUtils.fenToYuan(totalExpense);
    }

    private Integer totalRefund;

    @ExcelProperty("累计提现(元)")
    private BigDecimal totalRefundYuan;

    public BigDecimal getTotalRefundYuan() {
        if (totalRefund == null) return null;
        return MoneyUtils.fenToYuan(totalRefund);
    }

    private Integer totalBackend;


    @ExcelProperty("累计后台(元)")
    private BigDecimal totalBackendYuan;

    public BigDecimal getTotalBackendYuan() {
        if (totalBackend == null) return null;
        return MoneyUtils.fenToYuan(totalBackend);
    }

    private Integer freezePrice;

    @ExcelProperty("冻结金额(元)")
    private BigDecimal freezePriceYuan;

    public BigDecimal getFreezePriceYuan() {
        if (freezePrice == null) return null;
        return MoneyUtils.fenToYuan(freezePrice);
    }

    private Integer withdrawableBalance;

    @ExcelProperty("可提现金额(元)")
    private BigDecimal withdrawableBalanceYuan;

    public BigDecimal getWithdrawableBalanceYuan() {
        if (withdrawableBalance == null) return null;
        return MoneyUtils.fenToYuan(withdrawableBalance);
    }




    @ExcelProperty("所在市场")
    private String marketName;
    @ExcelProperty("商户地址")
    private String address;

    @ExcelProperty("公司名称")
    private String companyName;

    @ExcelProperty("税号")
    private String taxId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @ColumnWidth(20) // 设置更宽的列宽
    private LocalDateTime createTime;
    public Integer getWithdrawableBalance() {
        if(totalRecharge!=null&&totalRefund!=null&&expireRecharge!=null) {
            //累计充值减去过期充值
            totalRecharge = totalRecharge - expireRecharge;
            Integer withdrawableBalance = totalRecharge - totalRefund;
            //如果累计充值减去过期的充值小于0，则取0
            if (withdrawableBalance < 0) {
                withdrawableBalance = 0;
            }
            if (withdrawableBalance > balance) {
                withdrawableBalance = balance;
            }
            return withdrawableBalance;
        }
        return 0;
    }

    public void setWithdrawableBalance(Integer withdrawableBalance) {
        this.withdrawableBalance = withdrawableBalance;
    }
}
