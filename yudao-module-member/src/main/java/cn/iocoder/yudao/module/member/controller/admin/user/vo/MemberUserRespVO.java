package cn.iocoder.yudao.module.member.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会员用户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserRespVO extends MemberUserBaseVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23788")
    private Long id;

    @Schema(description = "注册 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "127.0.0.1")
    private String registerIp;

    @Schema(description = "最后登录IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "127.0.0.1")
    private String loginIp;

    @Schema(description = "最后登录时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime loginDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    // ========== 其它信息 ==========
    /**
     * 商户地址
     */
    private String address;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 税号
     */
    private String taxId;

    /**
     * 市场名称
     */
    private String marketName;
    //余额
    private Integer balance;

    /**
     * 过期充值
     */
    private Integer expireRecharge;

    //累计充值
    private Integer totalRecharge;
    //累计退款
    private Integer totalRefund;
    //可提现余额
    private Integer withdrawableBalance;
    //累计后台调整
    private Integer totalBackend;
    //冻结金额
    private Integer freezePrice;
    //累计消费
    private Integer totalExpense;


    //钱包id
    @ExcelProperty("钱包编号")
    private Long walletId;

    @ExcelProperty("商户名称")
    private String shopName;

    private Long invoiceType;

    /**
     * 联系人
     */
    private String contacts;
    public Integer getWithdrawableBalance() {
        if(totalRecharge!=null&&totalRefund!=null&&expireRecharge!=null) {
            //累计充值减去过期充值
            totalRecharge = totalRecharge - expireRecharge;
            Integer withdrawableBalance = totalRecharge - totalRefund;
            //如果累计充值减去过期的充值小于0，则取0
            if (withdrawableBalance < 0) {
                withdrawableBalance = 0;
            }
            if (withdrawableBalance > balance) {
                withdrawableBalance = balance;
            }
            return withdrawableBalance;
        }
        return 0;
    }

    public void setWithdrawableBalance(Integer withdrawableBalance) {
        this.withdrawableBalance = withdrawableBalance;
    }
}
