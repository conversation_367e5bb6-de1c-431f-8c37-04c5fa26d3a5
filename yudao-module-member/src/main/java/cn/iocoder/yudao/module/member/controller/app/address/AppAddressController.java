package cn.iocoder.yudao.module.member.controller.app.address;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.member.controller.app.address.vo.AppAddressCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.address.vo.AppAddressRespVO;
import cn.iocoder.yudao.module.member.controller.app.address.vo.AppAddressUpdateReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.address.MemberAddressDO;
import cn.iocoder.yudao.module.member.service.address.AddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 用户收件地址")
@RestController
@RequestMapping("/member/address")
@Validated
public class AppAddressController {

    @Resource
    private AddressService addressService;


    @PostMapping("/create")
    @Operation(summary = "创建用户地址")
    public CommonResult<Long> createAddress(@Valid @RequestBody AppAddressCreateReqVO createReqVO) {
        return success(addressService.createAddress(getLoginUserId(), createReqVO));
    }

    @TenantIgnore
    @PutMapping("/update")
    @Operation(summary = "更新用户地址")
    public CommonResult<Boolean> updateAddress(@Valid @RequestBody AppAddressUpdateReqVO updateReqVO) {
        addressService.updateAddress(getLoginUserId(), updateReqVO);
        return success(true);
    }

    @TenantIgnore
    @DeleteMapping("/delete")
    @Operation(summary = "删除用户地址")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteAddress(@RequestParam("id") Long id) {
        addressService.deleteAddress(getLoginUserId(), id);
        return success(true);
    }

    @TenantIgnore
    @GetMapping("/get")
    @Operation(summary = "获得用户地址")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppAddressRespVO> getAddress(@RequestParam("id") Long id) {
        MemberAddressDO address = addressService.getAddress(getLoginUserId(), id);
        return success(BeanUtil.toBean(address, AppAddressRespVO.class));
    }

    @TenantIgnore
    @GetMapping("/get-default")
    @Operation(summary = "获得默认的寄件地址")
    public CommonResult<AppAddressRespVO> getDefaultUserAddress() {
        MemberAddressDO address = addressService.getDefaultUserAddress(getLoginUserId());
        return success(BeanUtil.toBean(address, AppAddressRespVO.class));
    }

    @TenantIgnore
    @GetMapping("/list")
    @Operation(summary = "获得用户地址列表")
    @Parameter(name = "addressType", description = "地址类型：1-收件地址，2-寄件地址", required = true, example = "2")
    public CommonResult<List<AppAddressRespVO>> getAddressList(@RequestParam("addressType") Integer addressType) {
        List<MemberAddressDO> list = addressService.getAddressList(getLoginUserId(),addressType);
        return success(BeanUtil.copyToList(list, AppAddressRespVO.class));
    }

}
