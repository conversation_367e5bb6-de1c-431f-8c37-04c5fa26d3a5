<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.member.dal.mysql.market.MarketMapper">


    <select id="getMarketList" resultType="cn.iocoder.yudao.module.member.dal.dataobject.market.MarketDO">
        SELECT *,ROUND(6371 * ACOS(
                            COS(RADIANS(#{lat}))
                            * COS(RADIANS(lat))
                            * COS(RADIANS(lng) - RADIANS(#{lng}))
                        + SIN(RADIANS(#{lat}))
                                * SIN(RADIANS(lat))
                ), 2) AS distance_km
        FROM logistics_market
        WHERE deleted = 0
        ORDER BY distance_km ASC
    </select>

</mapper>