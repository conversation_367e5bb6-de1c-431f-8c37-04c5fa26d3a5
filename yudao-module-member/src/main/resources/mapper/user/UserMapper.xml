<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.member.dal.mysql.user.MemberUserMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectMemberUserPage"
            resultType="cn.iocoder.yudao.module.member.controller.admin.user.vo.MemberUserRespVO">
        SELECT a.*, b.name AS marketName,p.`balance`,(SELECT IFNULL(SUM(pay_price), 0) AS pay_price FROM pay_wallet_recharge
        WHERE wallet_id = p.`id` AND pay_status = 1
        <![CDATA[ AND pay_time < DATE_SUB(NOW(), INTERVAL 1 YEAR) AND deleted = 0)AS expireRecharge ]]>,
            p.`total_recharge`,p.`total_refund`,p.total_backend,p.freeze_price,p.`id` AS walletId,p.total_expense
        FROM member_user a
        LEFT JOIN logistics_market b ON a.market_id = b.id
        LEFT JOIN `pay_wallet` p ON p.`user_id`=a.`id`
        <where>
            <if test="reqVO.createTime != null and reqVO.createTime != ''">
                AND a.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
            </if>
            <if test="reqVO.mobile != null and reqVO.mobile != ''">
                AND a.mobile LIKE CONCAT('%', #{reqVO.mobile}, '%')
            </if>
            <if test="reqVO.shopName != null and reqVO.shopName != ''">
                AND a.shop_name LIKE CONCAT('%', #{reqVO.shopName}, '%')
            </if>
            <if test="reqVO.marketId != null and reqVO.marketId != ''">
                AND a.market_id = #{reqVO.marketId}
            </if>
            <if test="reqVO.id != null and reqVO.id != ''">
                AND a.id = #{reqVO.id}
            </if>
            and a.deleted = 0
        </where>
        order by a.create_time desc
    </select>
    <select id="getMessageForImport" resultType="java.util.Map">
        SELECT
            user_data.shopName,
            user_data.marketName,
            product_data.platformType
        FROM
            -- 用户数据子查询
            (SELECT
                 a.`shop_name` AS shopName,
                 b.name AS marketName
             FROM `member_user` a
                      LEFT JOIN `logistics_market` b ON b.`id` = a.`market_id`
             WHERE a.`id` =  #{userId}) AS user_data,

            -- 产品数据子查询
            (SELECT
                 platform_type as platformType
             FROM logistics_product
             WHERE name = #{name}) AS product_data;
    </select>
</mapper>

