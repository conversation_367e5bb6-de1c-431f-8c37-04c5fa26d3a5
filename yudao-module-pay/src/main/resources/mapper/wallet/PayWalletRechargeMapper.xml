<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.pay.dal.mysql.wallet.PayWalletRechargeMapper">


    <select id="getTotalExpireRecharge" resultType="java.lang.Integer">
     SELECT IFNULL(SUM(pay_price), 0) AS pay_price FROM pay_wallet_recharge
     WHERE wallet_id = #{walletId} AND pay_status = 1
    <![CDATA[ AND pay_time < DATE_SUB(NOW(), INTERVAL 1 YEAR) ]]> AND deleted = 0
    </select>
    <select id="selectWithdrawableWalletRechargeList"
            resultType="cn.iocoder.yudao.module.pay.dal.dataobject.wallet.PayWalletRechargeDO">
     SELECT * FROM pay_wallet_recharge WHERE wallet_id = #{walletId} AND pay_status = 1
    <![CDATA[ AND pay_time > DATE_SUB(NOW(), INTERVAL 1 YEAR) ]]> AND refund_status !=30 AND deleted = 0  ORDER BY create_time ASC
    </select>
    <select id="getUnFinishedWaybillNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `logistics_waybill` a
        WHERE a.`user_id` = #{userId}
        AND a.`waybill_status` !='2' AND a.`waybill_status` !='3'
        AND a.`payment_status`!='0' AND a.`payment_status`!='2'
        AND a.`logistics_status`!='6'
        AND a.`logistics_status`!='5' AND a.`logistics_status`!='4'
        AND a.`order_method` !='2'
    </select>
</mapper>