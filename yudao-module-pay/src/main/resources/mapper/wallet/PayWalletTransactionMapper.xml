<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.pay.dal.mysql.wallet.PayWalletTransactionMapper">


    <select id="selectWalletTransactionPage"
            resultType="cn.iocoder.yudao.module.pay.dal.dataobject.wallet.PayWalletTransactionDO">
        SELECT a.* FROM `pay_wallet_transaction` a LEFT JOIN `pay_wallet` b ON a.`wallet_id`=b.`id`
        LEFT JOIN `member_user` c ON c.`id`=b.`user_id`
        <where>
            <if test="bo.walletId != null and bo.walletId != ''">
             AND a.wallet_id = #{bo.walletId}
            </if>
            <if test="bo.bizType != null and bo.bizType != ''">
            AND a.biz_type = #{bo.bizType}
            </if>
            <if test="bo.bizId != null and bo.bizId != ''">
            AND a.biz_id = #{bo.bizId}
            </if>
            <if test="bo.marketId != null and bo.marketId != ''">
            AND c.market_id = #{bo.marketId}
            </if>
            AND a.deleted = 0
        </where>
        ORDER BY a.id DESC
    </select>
</mapper>