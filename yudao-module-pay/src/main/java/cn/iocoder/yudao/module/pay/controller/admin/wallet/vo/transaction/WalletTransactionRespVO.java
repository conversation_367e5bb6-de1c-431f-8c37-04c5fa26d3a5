package cn.iocoder.yudao.module.pay.controller.admin.wallet.vo.transaction;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会员钱包流水 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WalletTransactionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2082")
    private Long id;

    @Schema(description = "会员钱包 id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13714")
    @ExcelProperty("商户编号")
    @ColumnWidth(15)
    private Long walletId;

    @Schema(description = "流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("流水号")
    private String no;

    @Schema(description = "关联类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer bizType;

    @Schema(description = "流水标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易类型")
    private String title;

    @Schema(description = "关联业务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21930")
    @ExcelProperty("运单号")
    private String bizId;

    public String getBizId() {
        if(StrUtil.isNotEmpty(bizId)&&bizId.contains("SF")){
            return bizId;
        }
        return null;
    }




    @Schema(description = "交易金额, 单位分", requiredMode = Schema.RequiredMode.REQUIRED, example = "20382")
    private Integer price;

    @Schema(description = "交易金额（元）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("金额(元)")
    private BigDecimal balancePrice;

    public BigDecimal getBalancePrice() {
        if (price == null) return null;
        return MoneyUtils.fenToYuan(price);
    }



    @Schema(description = "余额, 单位分", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer balance;

    //新增元单位的字段（仅用于展示）
    @Schema(description = "余额（元）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("余额(元)")
    private BigDecimal balanceYuan;

    // 在 getBalanceYuan() 中实现分转元逻辑
    public BigDecimal getBalanceYuan() {
        if (balance == null) return null;
        return MoneyUtils.fenToYuan(balance);
    }
    @ExcelProperty("备注")
    private String remark;


    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @ColumnWidth(20) // 设置更宽的列宽
    private LocalDateTime createTime;

}