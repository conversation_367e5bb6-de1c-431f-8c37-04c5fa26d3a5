package cn.iocoder.yudao.module.pay.controller.admin.wallet.vo.transaction;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员钱包流水分页 Request VO")
@Data
public class WalletTransactionPageReqVO extends PageParam {

    @Schema(description = "会员钱包 id", example = "13714")
    private Long walletId;

    @Schema(description = "关联业务编号", example = "21930")
    private String bizId;

    private  Integer bizType;

    Long marketId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}