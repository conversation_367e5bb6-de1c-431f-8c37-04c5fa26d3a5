package cn.iocoder.yudao.module.pay.controller.admin.wallet;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pay.controller.admin.wallet.vo.transaction.WalletTransactionPageReqVO;
import cn.iocoder.yudao.module.pay.controller.admin.wallet.vo.transaction.WalletTransactionRespVO;
import cn.iocoder.yudao.module.pay.dal.dataobject.wallet.PayWalletTransactionDO;
import cn.iocoder.yudao.module.pay.service.wallet.PayWalletTransactionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getMemberUserId;

@Tag(name = "管理后台 - 钱包余额明细")
@RestController
@RequestMapping("/pay/wallet-transaction")
@Validated
@Slf4j
public class PayWalletTransactionController {

    @Resource
    private PayWalletTransactionService payWalletTransactionService;


    @GetMapping("/page")
    @Operation(summary = "获得会员钱包流水分页")
    public CommonResult<PageResult<WalletTransactionRespVO>> getWalletTransactionPage(@Valid WalletTransactionPageReqVO pageReqVO) {
        PageResult<PayWalletTransactionDO> pageResult = payWalletTransactionService.getWalletTransactionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WalletTransactionRespVO.class));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员钱包流水")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pay:wallet-transaction:delete')")
    public CommonResult<Boolean> deleteWalletTransaction(@RequestParam("id") Long id) {
        payWalletTransactionService.deleteWalletTransaction(id);
        return success(true);
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出会员钱包流水 Excel")
    @PreAuthorize("@ss.hasPermission('pay:wallet-transaction:export')")
    public void exportWalletTransactionExcel(@Valid WalletTransactionPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PayWalletTransactionDO> list = payWalletTransactionService.getWalletTransactionPage(pageReqVO).getList();

        Set<String> excludeColumns = new HashSet<>();
        //如果是商家端，要过滤掉一些字段
        Long memberUserId = getMemberUserId();
        if (memberUserId != null) {
            excludeColumns.add("remark");
        }
        // 导出 Excel
        ExcelUtils.write(response, "钱包流水.xlsx", "数据", WalletTransactionRespVO.class,
                BeanUtils.toBean(list, WalletTransactionRespVO.class),excludeColumns);

    }

}
