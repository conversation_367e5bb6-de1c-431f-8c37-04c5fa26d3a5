package cn.iocoder.yudao.module.pay.controller.app.wallet.vo.withdraw;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;

@Schema(description = "用户 APP - 提现 Request VO")
@Data
public class AppPayWithdrawReqVO {

    @Schema(description = "提现金额，分",  example = "1000")
    @Min(value = 1,  message = "提现金额必须大于零")
    private Integer amount;


}
