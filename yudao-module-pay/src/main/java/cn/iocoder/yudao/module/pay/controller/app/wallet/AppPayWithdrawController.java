package cn.iocoder.yudao.module.pay.controller.app.wallet;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pay.controller.admin.order.vo.PayOrderRespVO;
import cn.iocoder.yudao.module.pay.controller.app.wallet.vo.wallet.AppWithdrawableBalanceRespVO;
import cn.iocoder.yudao.module.pay.controller.app.wallet.vo.withdraw.AppPayWithdrawReqVO;
import cn.iocoder.yudao.module.pay.dal.dataobject.order.PayOrderDO;
import cn.iocoder.yudao.module.pay.dal.dataobject.wallet.PayWalletTransactionDO;
import cn.iocoder.yudao.module.pay.enums.order.PayOrderStatusEnum;
import cn.iocoder.yudao.module.pay.enums.wallet.PayWalletBizTypeEnum;
import cn.iocoder.yudao.module.pay.service.wallet.PayWalletRechargeService;
import cn.iocoder.yudao.module.pay.service.wallet.PayWalletService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/17
 */
@Tag(name = "用户 APP - 提现")
@RestController
@RequestMapping("/pay/withdraw")
@Validated
@Slf4j
public class AppPayWithdrawController {

    @Resource
    private PayWalletService payWalletService;

    @Resource
    private PayWalletRechargeService walletRechargeService;


    @GetMapping("/getWithdrawableBalance")
    @Operation(summary = "获取可提现余额")
    public CommonResult<AppWithdrawableBalanceRespVO> getWithdrawableBalance() {
        AppWithdrawableBalanceRespVO appWithdrawableBalanceRespVO = payWalletService.getWithdrawableBalance(getLoginUserId(), UserTypeEnum.MEMBER.getValue());
        return success(appWithdrawableBalanceRespVO);
    }

    @PostMapping("/withdraw")
    @Operation(summary = "提现")
    public CommonResult<Boolean> withdraw(
            @Valid @RequestBody AppPayWithdrawReqVO reqVO) {
        walletRechargeService.withdraw(reqVO, getClientIP(),getLoginUserId(), UserTypeEnum.MEMBER.getValue());
        return success(true);
    }

    //扣减余额
    //PayWalletBizType  Long walletId, String bizId,
    //                                            PayWalletBizTypeEnum bizType, Integer price,String remark
    //增加钱包余额


    @PermitAll
    @GetMapping("/addWalletBalance")
    @Operation(summary = "增加余额")
    public CommonResult<Boolean> addWalletBalance(@RequestParam("id") Long walletId,
                                                  @RequestParam("bizId")String bizId,
                                                  @RequestParam("bizType")Integer bizType,
                                                  @RequestParam("price")Integer price,
                                                  @RequestParam("remark")String remark) {
        payWalletService.addWalletBalance(walletId, bizId, PayWalletBizTypeEnum.valueOf(bizType), price, remark);
        return success(true);
    }

    @PermitAll
    @GetMapping("/reduceWalletBalance")
    @Operation(summary = "扣减余额")
    public CommonResult<Boolean> reduceWalletBalance(@RequestParam("id") Long walletId,
                                                  @RequestParam("bizId")String bizId,
                                                  @RequestParam("bizType")Integer bizType,
                                                  @RequestParam("price")Integer price,
                                                  @RequestParam("remark")String remark) {
        payWalletService.reduceWalletBalance(walletId, bizId, PayWalletBizTypeEnum.valueOf(bizType), price, remark);
        return success(true);
    }
}
