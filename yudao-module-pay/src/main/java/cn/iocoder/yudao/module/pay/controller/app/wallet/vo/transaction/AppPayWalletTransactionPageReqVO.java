package cn.iocoder.yudao.module.pay.controller.app.wallet.vo.transaction;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 钱包流水分页 Request VO")
@Data
public class AppPayWalletTransactionPageReqVO extends PageParam {

    /**
     * 类型 - 收入
     */
    public static final Integer TYPE_INCOME = 1;
    /**
     * 类型 - 支出
     */
    public static final Integer TYPE_EXPENSE = 2;

    @Schema(description = "类型，不传就是全部 1是充值 2是扣款 3是退款 4是提现",  example = "1")
    private Integer type;

    @Schema(description = "开始时间", example = "2025-06-14 14:29:29")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2025-06-14 14:29:29")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}
