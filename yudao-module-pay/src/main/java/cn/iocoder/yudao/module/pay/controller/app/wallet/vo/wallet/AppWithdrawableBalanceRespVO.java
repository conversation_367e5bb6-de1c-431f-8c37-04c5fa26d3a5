package cn.iocoder.yudao.module.pay.controller.app.wallet.vo.wallet;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "用户 APP - 可提现余额 Response VO")
@Data
@Builder
public class AppWithdrawableBalanceRespVO {

    @Schema(description = "可提现余额，单位分", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer withdrawableBalance;

    @Hidden
    @Schema(description = "会员钱包 id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25")
    private Long walletId;

}
