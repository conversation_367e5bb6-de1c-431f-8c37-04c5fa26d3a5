package cn.iocoder.yudao.module.logistics.dal.dataobject.market;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 批发市场 DO
 *
 * <AUTHOR>
 */
@TableName("logistics_market")
@KeySequence("logistics_market_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 市场名称
     */
    private String name;


}