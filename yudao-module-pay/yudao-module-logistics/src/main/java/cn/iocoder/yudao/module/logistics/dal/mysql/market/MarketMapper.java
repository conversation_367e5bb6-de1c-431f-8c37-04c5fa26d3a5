package cn.iocoder.yudao.module.logistics.dal.mysql.market;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.logistics.dal.dataobject.market.MarketDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.logistics.controller.admin.market.vo.*;

/**
 * 批发市场 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MarketMapper extends BaseMapperX<MarketDO> {

    default PageResult<MarketDO> selectPage(MarketPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MarketDO>()
                .likeIfPresent(MarketDO::getName, reqVO.getName())
                .betweenIfPresent(MarketDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MarketDO::getId));
    }

}