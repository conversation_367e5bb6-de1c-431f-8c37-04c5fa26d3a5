package cn.iocoder.yudao.module.logistics.controller.admin.market.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 批发市场 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MarketRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6531")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "市场名称", example = "李四")
    @ExcelProperty("市场名称")
    private String name;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}