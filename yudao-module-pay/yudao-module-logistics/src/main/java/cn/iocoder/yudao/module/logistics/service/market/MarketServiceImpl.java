package cn.iocoder.yudao.module.logistics.service.market;

import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.logistics.controller.admin.market.vo.*;
import cn.iocoder.yudao.module.logistics.dal.dataobject.market.MarketDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.logistics.dal.mysql.market.MarketMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.diffList;
import static cn.iocoder.yudao.module.logistics.enums.ErrorCodeConstants.*;

/**
 * 批发市场 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MarketServiceImpl implements MarketService {

    @Resource
    private MarketMapper marketMapper;

    @Override
    public Long createMarket(MarketSaveReqVO createReqVO) {
        // 插入
        MarketDO market = BeanUtils.toBean(createReqVO, MarketDO.class);
        marketMapper.insert(market);
        // 返回
        return market.getId();
    }

    @Override
    public void updateMarket(MarketSaveReqVO updateReqVO) {
        // 校验存在
        validateMarketExists(updateReqVO.getId());
        // 更新
        MarketDO updateObj = BeanUtils.toBean(updateReqVO, MarketDO.class);
        marketMapper.updateById(updateObj);
    }

    @Override
    public void deleteMarket(Long id) {
        // 校验存在
        validateMarketExists(id);
        // 删除
        marketMapper.deleteById(id);
    }

    @Override
        public void deleteMarketListByIds(List<Long> ids) {
        // 校验存在
        validateMarketExists(ids);
        // 删除
        marketMapper.deleteByIds(ids);
        }

    private void validateMarketExists(List<Long> ids) {
        List<MarketDO> list = marketMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(MARKET_NOT_EXISTS);
        }
    }

    private void validateMarketExists(Long id) {
        if (marketMapper.selectById(id) == null) {
            throw exception(MARKET_NOT_EXISTS);
        }
    }

    @Override
    public MarketDO getMarket(Long id) {
        return marketMapper.selectById(id);
    }

    @Override
    public PageResult<MarketDO> getMarketPage(MarketPageReqVO pageReqVO) {
        return marketMapper.selectPage(pageReqVO);
    }

}