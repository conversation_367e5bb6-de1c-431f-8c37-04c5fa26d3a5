package cn.iocoder.yudao.module.logistics.controller.admin.market.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 批发市场新增/修改 Request VO")
@Data
public class MarketSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6531")
    private Long id;

    @Schema(description = "市场名称", example = "李四")
    private String name;

}