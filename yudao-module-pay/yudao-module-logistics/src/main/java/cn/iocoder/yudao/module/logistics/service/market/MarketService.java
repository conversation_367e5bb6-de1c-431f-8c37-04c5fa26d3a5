package cn.iocoder.yudao.module.logistics.service.market;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.logistics.controller.admin.market.vo.*;
import cn.iocoder.yudao.module.logistics.dal.dataobject.market.MarketDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 批发市场 Service 接口
 *
 * <AUTHOR>
 */
public interface MarketService {

    /**
     * 创建批发市场
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMarket(@Valid MarketSaveReqVO createReqVO);

    /**
     * 更新批发市场
     *
     * @param updateReqVO 更新信息
     */
    void updateMarket(@Valid MarketSaveReqVO updateReqVO);

    /**
     * 删除批发市场
     *
     * @param id 编号
     */
    void deleteMarket(Long id);

    /**
    * 批量删除批发市场
    *
    * @param ids 编号
    */
    void deleteMarketListByIds(List<Long> ids);

    /**
     * 获得批发市场
     *
     * @param id 编号
     * @return 批发市场
     */
    MarketDO getMarket(Long id);

    /**
     * 获得批发市场分页
     *
     * @param pageReqVO 分页查询
     * @return 批发市场分页
     */
    PageResult<MarketDO> getMarketPage(MarketPageReqVO pageReqVO);

}