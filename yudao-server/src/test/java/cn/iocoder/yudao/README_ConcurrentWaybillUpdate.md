# 并发运单更新测试类使用说明

## 📋 概述

`ConcurrentWaybillUpdateTest` 是一个专门用于并发获取、更新特定时间范围内特定物流状态的老系统运单数据的测试类。

### 🎯 主要功能

支持并发更新以下字段：
- **轨迹数据** - 从物流API获取最新轨迹信息
- **物流状态** - 根据轨迹数据更新物流状态
- **真实重量** - 更新运单的实际重量
- **揽收时间** - 更新快递揽收时间
- **收件省份** - 标准化收件人省份信息
- **市场产品ID** - 更新产品市场关联ID
- **差额补扣费/退款** - 计算并执行钱包操作

### 🔧 技术特点

- **并发处理**：使用线程池并发处理，提高处理效率
- **完整留痕**：所有操作都有详细记录，包括原始值、新值、操作结果
- **预览模式**：支持预览模式，可以先查看预期结果再决定是否执行
- **灵活配置**：可以单独更新某个字段，也可以批量更新所有字段
- **智能优化**：轨迹数据、物流状态、真实重量、揽收时间优先使用运单自身数据，减少API调用
- **错误处理**：完善的异常处理机制，单个运单失败不影响整体处理

## 🚀 主要测试方法

### 1. 并发更新所有字段

#### 预览模式
```java
@Test
public void concurrentUpdateAllFieldsPreview()
```
**功能**：并发更新所有字段（预览模式）
**安全性**：✅ 安全，不会修改数据库
**用途**：查看预期的更新结果和统计信息

#### 真实入库模式
```java
@Test
public void concurrentUpdateAllFieldsReal()
```
**功能**：并发更新所有字段（真实入库模式）
**安全性**：⚠️ 谨慎使用，会实际修改数据库
**用途**：执行真实的数据更新操作

### 2. 单独字段更新

#### 轨迹数据更新
```java
@Test
public void updateRouteDataOnlyPreview()
```
**功能**：只更新轨迹数据
**适用场景**：轨迹数据缺失或需要刷新的运单

#### 差额补扣费/退款更新
```java
@Test
public void updateAmountDifferenceOnlyPreview()
```
**功能**：只计算并执行差额补扣费/退款
**适用场景**：需要重新计算运费差额的运单

## 📊 数据处理逻辑

### 查询条件
- **时间范围**：2025年7月1日至14日5点
- **订单来源**：老系统导入（orderMethod = OLD_IMPORT）
- **物流状态**：已消单、未收件、已收件、运输中、派件中、已签收

### 智能数据更新策略

#### 轨迹数据更新优先级
1. **优先使用运单自身轨迹**：如果运单已有轨迹数据，直接使用
2. **拉取最新轨迹**：只有在轨迹数据为空时才调用API获取最新轨迹

#### 物流状态更新优先级
1. **优先使用运单自身状态**：如果运单已有物流状态，直接使用
2. **从现有轨迹数据解析**：如果运单有轨迹数据，从中解析最新状态
3. **拉取最新轨迹**：只有在没有轨迹数据时才调用API获取最新轨迹

#### 真实重量更新优先级
1. **优先使用运单自身重量**：如果运单已有真实重量，直接使用
2. **从现有轨迹数据解析**：如果运单有轨迹数据，从中查找重量信息
3. **拉取最新轨迹**：只有在重量为空且没有轨迹数据时才调用API获取

#### 揽收时间更新优先级
1. **优先使用运单自身时间**：如果运单已有揽收时间，直接使用
2. **从现有轨迹数据解析**：如果运单有轨迹数据，从中查找揽收记录
3. **拉取最新轨迹**：只有在没有轨迹数据时才调用API获取最新轨迹

#### 收件省份更新优先级
1. **优先使用运单自身省份**：如果运单已有收件省份，直接使用
2. **系统地址解析工具**：如果省份为空，首先使用 `AddressValidatorUtil.getProvince()` 从地址中提取
3. **自定义省份提取方法**：如果系统工具无法解析，使用自定义的 `extractProvinceFromAddress()` 方法

#### 自定义省份提取功能
针对复杂地址格式，实现了智能省份提取算法，**独立封装为工具类**：

**架构设计**：
- **工具类**：`AddressProvinceExtractor` - 独立的地址省份提取工具
- **调用方式**：`AddressProvinceExtractor.extractProvince(address)`
- **代码复用**：可在项目其他模块中复用
- **维护性**：映射关系集中管理，便于更新维护

**地址清理功能**：
- 移除地址前缀干扰信息（如：转接号码、商品信息、收件人姓名等）
- 清理特殊字符和数字前缀
- 标准化地址格式

**省份匹配策略**：
1. **北京市区优先匹配**：识别北京16个区，优先匹配为北京市
2. **直接省份匹配**：匹配省份全称、简称、别称
3. **城市反推省份**：通过知名城市推断所属省份
4. **多种写法支持**：支持各种省份表达方式

**全国省市区完整映射**：
- **直辖市**：北京市（16个区）、上海市（16个区）、天津市（16个区）、重庆市（38个区县）
- **省份**：23个省的所有地级市、县级市、市辖区
- **自治区**：5个自治区的所有地级市、盟、州
- **特别行政区**：香港、澳门

**北京市区优先识别**：
- 城区：东城区、西城区、朝阳区、丰台区、石景山区、海淀区
- 远郊区：门头沟区、房山区、通州区、顺义区、昌平区、大兴区、怀柔区、平谷区、密云区、延庆区

**支持的复杂地址格式**：
- 带前缀的地址：`/北京市北京市丰台区...`
- 带转接信息：`（转接5032）北京市门头沟区...`
- 带商品信息：`草莓10盒北京市密云区...`
- 带收件人信息：`李先生，18533746547-4252，河北省廊坊市...`
- 地区选择格式：`所在地区北京北京市通州区...`
- 只有区名的地址：`大兴区兴政街15号...` → `北京市`
- 只有城市名：`三河市燕郊镇...` → `河北省`
- 标准城市格式：`唐山市迁西县...` → `河北省`

**匹配优先级示例**：
```
地址：大兴区兴政街15号大兴区人民政府
匹配：大兴区 → 北京市

地址：三河市燕郊镇半壁店村冶金路金瑞驰汽修厂
匹配：三河市 → 河北省

地址：唐山市迁西县栗乡街道东湖湾三期
匹配：唐山市 → 河北省

地址：苏州市吴江区吴越祥院10-202
匹配：苏州市 → 江苏省

地址：沈阳市大东区东中街大悦城
匹配：沈阳市 → 辽宁省

地址：郑州市金水区三全路文化路
匹配：郑州市 → 河南省
```

**覆盖范围统计**：
- **直辖市**：4个（北京、上海、天津、重庆）+ 所有区县
- **省份**：23个 + 所有地级市、县级市、市辖区
- **自治区**：5个（内蒙古、广西、西藏、宁夏、新疆）+ 所有地级市
- **特别行政区**：2个（香港、澳门）
- **总计**：覆盖全国34个省级行政区 + 300+个地级市 + 2000+个区县

### 🛠️ 工具类使用说明

#### AddressProvinceExtractor 工具类

**文件位置**：`yudao-server/src/test/java/cn/iocoder/yudao/util/AddressProvinceExtractor.java`

**使用方法**：

```java


// 提取省份
String province = AddressProvinceExtractor.extractProvince("大兴区兴政街15号大兴区人民政府");
// 返回：北京市

        String province2 = AddressProvinceExtractor.extractProvince("三河市燕郊镇半壁店村冶金路金瑞驰汽修厂");
// 返回：河北省
```

**核心特性**：
- ✅ **静态方法调用**：无需实例化，直接调用
- ✅ **完整映射覆盖**：全国34个省级行政区完整覆盖
- ✅ **智能地址清理**：自动处理复杂地址格式
- ✅ **多级匹配策略**：省份→北京区县→城市→区县四级匹配
- ✅ **高性能设计**：内存映射表，毫秒级响应

**集成到现有项目**：
```java
// 在运单更新逻辑中使用
if (StrUtil.isBlank(receiverProvince)) {
    // 首先尝试系统工具
    receiverProvince = AddressValidatorUtil.getProvince(receiverAddress);

    // 系统工具失败时使用自定义工具
    if (StrUtil.isBlank(receiverProvince)) {
        receiverProvince = AddressProvinceExtractor.extractProvince(receiverAddress);
    }
}
```

#### 市场产品ID更新优先级
1. **优先使用运单自身ID**：如果运单已有市场产品ID，直接使用
2. **从产品名称和市场ID获取**：如果市场产品ID为空，使用 `productService.getOneByProductNameAndMarketId()` 获取

### 修订的费用计算规则
1. **新应收金额** = 根据实际重量重新计算的应收金额
2. **原总金额** = 原已付金额 + 原退款金额 + 原补扣金额
3. **费用差额** = 新应收金额 - 原总金额
4. **操作规则**：
   - 费用差额 > 0：执行补扣操作，补扣金额为费用差额
   - 费用差额 < 0：执行退款操作，退款金额为费用差额的绝对值
   - 费用差额 = 0：无需操作

### 费用计算示例
```
示例1：需要补扣
新应收金额：1000分
原预估金额：800分，原退款金额：0分，原补扣金额：0分
原总金额：800 + 0 + 0 = 800分
费用差额：1000 - 800 = 200分 > 0
操作：补扣200分

示例2：需要退款
新应收金额：600分
原预估金额：800分，原退款金额：0分，原补扣金额：100分
原总金额：800 + 0 + 100 = 900分
费用差额：600 - 900 = -300分 < 0
操作：退款300分
```

### 钱包操作接口调用
- **补扣操作**：调用 `payWalletService.reduceWalletBalance()`
  - 传递参数：费用差额（正数）
  - 接口内部：处理为减少余额操作
- **退款操作**：调用 `payWalletService.addWalletBalance()`
  - 传递参数：费用差额的绝对值（正数）
  - 接口内部：处理为增加余额操作
- **记录流水**：保存钱包流水号和余额变化

### 接口参数传递规则
```java
// 补扣操作：费用差额 > 0
if (amountDifference > 0) {
    // 传递正数给 reduceWalletBalance 接口
    payWalletService.reduceWalletBalance(walletId, waybillNo, bizType, amountDifference, remark);
}

// 退款操作：费用差额 < 0
if (amountDifference < 0) {
    Integer refundAmount = Math.abs(amountDifference); // 转为正数
    // 传递正数给 addWalletBalance 接口
    payWalletService.addWalletBalance(walletId, waybillNo, bizType, refundAmount, remark);
}
```

## 📄 输出文件

### CSV记录文件
**文件名格式**：`concurrent_waybill_update_{更新类型}_{操作模式}_{时间戳}.csv`

**包含字段**：
- 运单基本信息：运单号、用户ID、下单时间等
- 原始数据：原物流状态、原轨迹数据、原重量、原收件省份、原收件地址等
- 更新数据：新物流状态、新轨迹数据、新重量、新收件省份等
- 金额计算：计算应收金额、应付金额、差额等
- 钱包操作：操作类型、流水号、余额变化等
- 处理信息：是否成功、错误信息、处理时间等

**省份获取分析字段**：
- **原收件地址**：完整的收件人地址信息，用于分析省份提取失败的原因
- **原收件省份**：运单中原有的省份信息
- **新收件省份**：更新后的省份信息

通过对比原收件地址和省份字段，可以分析：
- 地址格式是否标准
- 省份提取算法是否正确
- 哪些地址格式导致提取失败

## 🔧 配置参数

### 线程池配置
```java
private static final int THREAD_POOL_SIZE = 10;
```
可根据服务器性能调整并发线程数

### 更新类型枚举
```java
public enum UpdateType {
    ROUTE_DATA("轨迹数据"),
    LOGISTICS_STATUS("物流状态"),
    ACTUAL_WEIGHT("真实重量"),
    COLLECT_TIME("揽收时间"),
    RECEIVER_PROVINCE("收件省份"),
    PRODUCT_MARKET_ID("市场产品ID"),
    AMOUNT_DIFFERENCE("差额补扣费/退款"),
    ALL("全部");
}
```

## 📊 统计信息

处理完成后会输出详细的统计信息：
- 总处理数量、成功数量、失败数量
- 各字段更新数量统计
- 钱包操作统计（补扣/退款数量和金额）
- 处理时间统计（总时间、平均时间）
- 错误信息汇总

## ⚠️ 注意事项

1. **数据备份**：执行真实入库操作前，建议先备份相关数据
2. **预览验证**：建议先运行预览模式，确认结果无误后再执行真实操作
3. **并发控制**：注意线程池大小设置，避免对数据库造成过大压力
4. **API限流**：物流轨迹查询可能有频率限制，注意控制请求频率
5. **错误处理**：关注错误日志，及时处理异常情况

## 🚀 使用建议

### 推荐执行顺序
1. **预览全部字段更新**：`concurrentUpdateAllFieldsPreview()`
2. **分析预览结果**：检查CSV文件中的预期更新内容
3. **单独测试问题字段**：如有异常，单独测试相关字段更新
4. **执行真实更新**：确认无误后执行 `concurrentUpdateAllFieldsReal()`

### 性能优化建议
- 根据服务器性能调整线程池大小
- 分批处理大量数据，避免内存溢出
- 监控数据库连接池使用情况
- 适当增加API调用间隔，避免触发限流

## 📈 扩展功能

该测试类设计为可扩展的架构，可以轻松添加新的更新字段类型：

1. 在 `UpdateType` 枚举中添加新类型
2. 实现对应的 `updateXxxOnly()` 方法
3. 在 `updateAllFields()` 中调用新方法
4. 更新统计计数器和CSV输出格式

这样可以根据业务需求灵活扩展功能，满足不同的数据更新需求。
