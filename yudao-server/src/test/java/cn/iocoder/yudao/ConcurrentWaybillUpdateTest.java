package cn.iocoder.yudao;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.logistics.dal.dataobject.waybill.WaybillDO;
import cn.iocoder.yudao.module.logistics.dal.mysql.waybill.WaybillMapper;
import cn.iocoder.yudao.module.logistics.enums.waybill.LogisticsStatusEnum;
import cn.iocoder.yudao.module.logistics.enums.waybill.OrderMethodEnum;
import cn.iocoder.yudao.module.logistics.enums.shipping.ShippingTemplateTypeEnum;
import cn.iocoder.yudao.module.logistics.service.product.ProductService;
import cn.iocoder.yudao.module.logistics.service.shippingtemplate.ShippingTemplateService;
import cn.iocoder.yudao.module.logistics.util.ExpressAPIUtil;
import cn.iocoder.yudao.module.logistics.util.AddressValidatorUtil;
import cn.iocoder.yudao.module.pay.dal.dataobject.wallet.PayWalletDO;
import cn.iocoder.yudao.module.pay.dal.dataobject.wallet.PayWalletTransactionDO;
import cn.iocoder.yudao.module.pay.dal.mysql.wallet.PayWalletMapper;
import cn.iocoder.yudao.module.pay.enums.wallet.PayWalletBizTypeEnum;
import cn.iocoder.yudao.module.pay.service.wallet.PayWalletService;
import cn.iocoder.yudao.server.YudaoServerApplication;
import cn.iocoder.yudao.util.AddressProvinceExtractor;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并发运单更新测试类
 * 支持并发获取、更新特定时间范围内特定物流状态的老系统运单的各种数据
 * 包括：轨迹数据、物流状态、真实重量、揽收时间、收件省份、市场产品id、差额补扣费/退款
 * 
 * <AUTHOR>
 * @since 2025/8/4
 */
@SpringBootTest
@ContextConfiguration(classes = YudaoServerApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class ConcurrentWaybillUpdateTest {

    @Resource
    private WaybillMapper waybillMapper;
    
    @Resource
    private ShippingTemplateService shippingTemplateService;

    @Resource
    private PayWalletService payWalletService;

    @Resource
    private PayWalletMapper payWalletMapper;

    @Resource
    private ProductService productService;

    // 线程池大小
    private static final int THREAD_POOL_SIZE = 1000;

    // 更新类型枚举
    public enum UpdateType {
        ROUTE_DATA("轨迹数据"),
        LOGISTICS_STATUS("物流状态"),
        ACTUAL_WEIGHT("真实重量"),
        COLLECT_TIME("揽收时间"),
        RECEIVER_PROVINCE("收件省份"),
        PROVINCE_EXTRACT("省份提取"),
        PRODUCT_MARKET_ID("市场产品ID"),
        AMOUNT_DIFFERENCE("差额补扣费/退款"),
        ALL("全部");

        private final String description;

        UpdateType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 运单更新记录
    public static class WaybillUpdateRecord {
        String waybillNo;                           // 运单号
        Long waybillId;                             // 运单ID
        Long userId;                                // 用户ID
        LocalDateTime orderTime;                    // 下单时间
        Integer originalLogisticsStatus;            // 原物流状态
        String originalLogisticsStatusName;         // 原物流状态名称
        String originalRouteData;                   // 原轨迹数据
        BigDecimal originalActualWeight;            // 原真实重量
        LocalDateTime originalCollectTime;          // 原揽收时间
        String originalReceiverProvince;            // 原收件省份
        String originalReceiverAddress;             // 原收件地址
        Long originalProductMarketId;               // 原市场产品ID
        Integer originalSupplementAmount;           // 原补扣金额(分)
        Integer originalRefundAmount;               // 原退款金额(分)
        Integer originalEstimatedAmount;            // 原预估金额(分)
        Integer originalPaidAmount;                 // 原已付金额(分)
        
        Integer newLogisticsStatus;                 // 新物流状态
        String newLogisticsStatusName;              // 新物流状态名称
        String newRouteData;                        // 新轨迹数据
        BigDecimal newActualWeight;                 // 新真实重量
        LocalDateTime newCollectTime;               // 新揽收时间
        String newReceiverProvince;                 // 新收件省份
        Long newProductMarketId;                    // 新市场产品ID
        Integer newSupplementAmount;                // 新补扣金额(分)
        Integer newRefundAmount;                    // 新退款金额(分)
        
        BigDecimal calculatedReceivableAmount;      // 计算的应收金额(分)
        BigDecimal calculatedPayableAmount;         // 计算的应付金额(分)
        Integer amountDifference;                   // 与原订单差额(分)
        String walletOperationType;                 // 钱包操作类型
        String walletTransactionNo;                 // 钱包流水号
        BigDecimal walletBalanceBefore;             // 操作前钱包余额(元)
        BigDecimal walletBalanceAfter;              // 操作后钱包余额(元)
        
        boolean success;                            // 是否成功
        String errorMessage;                        // 错误信息
        UpdateType updateType;                      // 更新类型
        boolean isPreview;                          // 是否预览模式
        LocalDateTime processTime;                  // 处理时间
        long processDuration;                       // 处理耗时(毫秒)
        String threadName;                          // 处理线程名

        public WaybillUpdateRecord(String waybillNo, UpdateType updateType, boolean isPreview) {
            this.waybillNo = waybillNo;
            this.updateType = updateType;
            this.isPreview = isPreview;
            this.processTime = LocalDateTime.now();
            this.threadName = Thread.currentThread().getName();
        }
    }

    // 批量更新统计
    public static class BatchUpdateStatistics {
        int totalCount = 0;                         // 总数量
        int successCount = 0;                       // 成功数量
        int failCount = 0;                          // 失败数量
        int routeUpdateCount = 0;                   // 轨迹更新数量
        int statusUpdateCount = 0;                  // 状态更新数量
        int weightUpdateCount = 0;                  // 重量更新数量
        int collectTimeUpdateCount = 0;             // 揽收时间更新数量
        int provinceUpdateCount = 0;                // 省份更新数量
        int productMarketUpdateCount = 0;           // 产品市场更新数量
        int supplementCount = 0;                    // 补扣数量
        int refundCount = 0;                        // 退款数量
        BigDecimal totalSupplementAmount = BigDecimal.ZERO;  // 总补扣金额(元)
        BigDecimal totalRefundAmount = BigDecimal.ZERO;      // 总退款金额(元)
        long totalProcessTime = 0;                  // 总处理时间(毫秒)
        List<WaybillUpdateRecord> records = new ArrayList<>();  // 详细记录
        List<String> errorMessages = new ArrayList<>();         // 错误信息
    }

    /**
     * 并发更新所有字段 - 预览模式
     */
    @Test
    public void concurrentUpdateAllFieldsPreview() {
        log.info("🚀 开始并发更新所有字段（预览模式）...");
        
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 14, 5, 0, 0);
        
        List<Integer> targetLogisticsStatus = Arrays.asList(
            LogisticsStatusEnum.CANCELLED.getStatus(),      // 已消单
            LogisticsStatusEnum.UNCOLLECTED.getStatus(),    // 未收件
            LogisticsStatusEnum.COLLECTED.getStatus(),      // 已收件
            LogisticsStatusEnum.IN_TRANSIT.getStatus(),     // 运输中
            LogisticsStatusEnum.OUT_FOR_DELIVERY.getStatus(), // 派件中
            LogisticsStatusEnum.DELIVERED.getStatus() ,      // 已签收
                // 异常件、已退回
                 LogisticsStatusEnum.EXCEPTION.getStatus(),
                 LogisticsStatusEnum.RETURNED.getStatus()
        );
        
        concurrentUpdateWaybills(startTime, endTime, targetLogisticsStatus, UpdateType.ALL, true);
    }

    /**
     * 并发更新所有字段 - 真实入库模式
     */
    @Test
    public void concurrentUpdateAllFieldsReal() {
        log.info("🚀 开始并发更新所有字段（真实入库模式）...");
        
        LocalDateTime startTime = LocalDateTime.of(2025, 4, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        
        List<Integer> targetLogisticsStatus = Arrays.asList(
            LogisticsStatusEnum.CANCELLED.getStatus(),      // 已消单
            LogisticsStatusEnum.UNCOLLECTED.getStatus(),    // 未收件
            LogisticsStatusEnum.COLLECTED.getStatus(),      // 已收件
            LogisticsStatusEnum.IN_TRANSIT.getStatus(),     // 运输中
            LogisticsStatusEnum.OUT_FOR_DELIVERY.getStatus(), // 派件中
            LogisticsStatusEnum.DELIVERED.getStatus() ,     // 已签收
                // 异常件、已退回
                LogisticsStatusEnum.EXCEPTION.getStatus(),
                LogisticsStatusEnum.RETURNED.getStatus()
        );
        
        concurrentUpdateWaybills(startTime, endTime, targetLogisticsStatus, UpdateType.ALL, false);
    }

    /**
     * 单独更新轨迹数据 - 预览模式
     */
    @Test
    public void updateRouteDataOnlyPreview() {
        log.info("📋 开始单独更新轨迹数据（预览模式）...");
        
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 14, 5, 0, 0);
        
        List<Integer> targetLogisticsStatus = Arrays.asList(
            LogisticsStatusEnum.COLLECTED.getStatus(),      // 已收件
            LogisticsStatusEnum.IN_TRANSIT.getStatus(),     // 运输中
            LogisticsStatusEnum.OUT_FOR_DELIVERY.getStatus(), // 派件中
            LogisticsStatusEnum.DELIVERED.getStatus() ,      // 已签收
                // 异常件、已退回
                LogisticsStatusEnum.EXCEPTION.getStatus(),
                LogisticsStatusEnum.RETURNED.getStatus()
        );
        
        concurrentUpdateWaybills(startTime, endTime, targetLogisticsStatus, UpdateType.ROUTE_DATA, true);
    }

    /**
     * 单独更新差额补扣费/退款 - 预览模式
     */
    @Test
    public void updateAmountDifferenceOnlyPreview() {
        log.info("💰 开始单独更新差额补扣费/退款（预览模式）...");
        
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 14, 5, 0, 0);
        
        List<Integer> targetLogisticsStatus = Arrays.asList(
            LogisticsStatusEnum.CANCELLED.getStatus(),      // 已消单
            LogisticsStatusEnum.UNCOLLECTED.getStatus(),    // 未收件
            LogisticsStatusEnum.COLLECTED.getStatus(),      // 已收件
            LogisticsStatusEnum.IN_TRANSIT.getStatus(),     // 运输中
            LogisticsStatusEnum.OUT_FOR_DELIVERY.getStatus(), // 派件中
            LogisticsStatusEnum.DELIVERED.getStatus() ,      // 已签收
                // 异常件、已退回
                LogisticsStatusEnum.EXCEPTION.getStatus(),
                LogisticsStatusEnum.RETURNED.getStatus()
        );
        
        concurrentUpdateWaybills(startTime, endTime, targetLogisticsStatus, UpdateType.AMOUNT_DIFFERENCE, true);
    }

    /**
     * 单独更新省份提取 - 预览模式
     */
    @Test
    public void updateProvinceExtractOnlyPreview() {
        log.info("🏠 开始单独更新省份提取（预览模式）...");

        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 14, 5, 0, 0);

        List<Integer> targetLogisticsStatus = Arrays.asList(
            LogisticsStatusEnum.CANCELLED.getStatus(),      // 已消单
            LogisticsStatusEnum.UNCOLLECTED.getStatus(),    // 未收件
            LogisticsStatusEnum.COLLECTED.getStatus(),      // 已收件
            LogisticsStatusEnum.IN_TRANSIT.getStatus(),     // 运输中
            LogisticsStatusEnum.OUT_FOR_DELIVERY.getStatus(), // 派件中
            LogisticsStatusEnum.DELIVERED.getStatus(),      // 已签收
            LogisticsStatusEnum.EXCEPTION.getStatus(),      // 异常件
            LogisticsStatusEnum.RETURNED.getStatus()        // 已退回
        );

        concurrentUpdateWaybills(startTime, endTime, targetLogisticsStatus, UpdateType.PROVINCE_EXTRACT, true);
    }

    /**
     * 单独更新省份提取 - 真实更新
     */
    @Test
    public void updateProvinceExtractOnlyReal() {
        log.info("🏠 开始单独更新省份提取（真实更新）...");

        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 14, 5, 0, 0);

        List<Integer> targetLogisticsStatus = Arrays.asList(
            LogisticsStatusEnum.CANCELLED.getStatus(),      // 已消单
            LogisticsStatusEnum.UNCOLLECTED.getStatus(),    // 未收件
            LogisticsStatusEnum.COLLECTED.getStatus(),      // 已收件
            LogisticsStatusEnum.IN_TRANSIT.getStatus(),     // 运输中
            LogisticsStatusEnum.OUT_FOR_DELIVERY.getStatus(), // 派件中
            LogisticsStatusEnum.DELIVERED.getStatus(),      // 已签收
            LogisticsStatusEnum.EXCEPTION.getStatus(),      // 异常件
            LogisticsStatusEnum.RETURNED.getStatus()        // 已退回
        );

        concurrentUpdateWaybills(startTime, endTime, targetLogisticsStatus, UpdateType.PROVINCE_EXTRACT, false);
    }

    /**
     * 测试省份提取功能 - 小范围测试
     */
    @Test
    public void testProvinceExtractSmall() {
        log.info("🏠 开始小范围测试省份提取功能...");

        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 1, 1, 0, 0); // 只测试1小时的数据

        List<Integer> targetLogisticsStatus = Arrays.asList(
            LogisticsStatusEnum.DELIVERED.getStatus()  // 只测试已签收的运单
        );

        concurrentUpdateWaybills(startTime, endTime, targetLogisticsStatus, UpdateType.PROVINCE_EXTRACT, true);
    }

    /**
     * 并发更新运单核心方法
     */
    private void concurrentUpdateWaybills(LocalDateTime startTime, LocalDateTime endTime,
                                        List<Integer> targetLogisticsStatus, UpdateType updateType, boolean isPreview) {

        BatchUpdateStatistics statistics = new BatchUpdateStatistics();
        long startProcessTime = System.currentTimeMillis();

        try {
            // 查询符合条件的运单
            List<WaybillDO> waybillList = queryTargetWaybills(startTime, endTime, targetLogisticsStatus);
            statistics.totalCount = waybillList.size();

            log.info("📊 查询到符合条件的运单数量：{}", waybillList.size());
            log.info("🔧 更新类型：{}", updateType.getDescription());
            log.info("🔍 操作模式：{}", isPreview ? "预览模式" : "真实入库模式");

            if (waybillList.isEmpty()) {
                log.info("✅ 没有符合条件的运单需要处理");
                return;
            }

            // 创建线程池
            ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
            AtomicInteger processedCount = new AtomicInteger(0);

            try {
                // 创建并发任务
                List<CompletableFuture<WaybillUpdateRecord>> futures = new ArrayList<>();

                for (WaybillDO waybill : waybillList) {
                    CompletableFuture<WaybillUpdateRecord> future = CompletableFuture.supplyAsync(() -> {
                        int currentCount = processedCount.incrementAndGet();
                        if (currentCount % 100 == 0) {
                            log.info("📈 处理进度：{}/{}", currentCount, waybillList.size());
                        }
                        return processWaybillUpdate(waybill, updateType, isPreview);
                    }, executorService);

                    futures.add(future);
                }

                // 等待所有任务完成
                log.info("⏳ 等待所有任务完成...");
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
                );

                allFutures.get(30, TimeUnit.MINUTES); // 最多等待30分钟

                // 收集结果
                for (CompletableFuture<WaybillUpdateRecord> future : futures) {
                    try {
                        WaybillUpdateRecord record = future.get();
                        statistics.records.add(record);

                        if (record.success) {
                            statistics.successCount++;
                            updateStatisticsCounters(statistics, record);
                        } else {
                            statistics.failCount++;
                            statistics.errorMessages.add(String.format("运单号：%s，错误：%s",
                                record.waybillNo, record.errorMessage));
                        }

                        statistics.totalProcessTime += record.processDuration;

                    } catch (Exception e) {
                        log.error("❌ 获取任务结果失败：{}", e.getMessage());
                        statistics.failCount++;
                    }
                }

            } finally {
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(5, TimeUnit.MINUTES)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

        } catch (Exception e) {
            log.error("❌ 并发更新运单失败：{}", e.getMessage(), e);
        } finally {
            long endProcessTime = System.currentTimeMillis();
            long totalTime = endProcessTime - startProcessTime;

            // 输出统计信息
            printBatchStatistics(statistics, updateType, isPreview, totalTime);

            // 保存详细记录
            saveBatchUpdateRecords(statistics, updateType, isPreview);
        }
    }

    /**
     * 查询目标运单
     */
    private List<WaybillDO> queryTargetWaybills(LocalDateTime startTime, LocalDateTime endTime,
                                               List<Integer> targetLogisticsStatus) {
        return waybillMapper.selectList(new LambdaQueryWrapperX<WaybillDO>()
                .between(WaybillDO::getOrderTime, startTime, endTime)
                .eq(WaybillDO::getOrderMethod, OrderMethodEnum.OLD_IMPORT.getStatus())
                .in(WaybillDO::getLogisticsStatus, targetLogisticsStatus)
                .isNotNull(WaybillDO::getWaybillNo)
                // 轨迹为空
//                .and(wrapper -> wrapper.isNull(WaybillDO::getWaybillRoute)
//                        .or().eq(WaybillDO::getWaybillRoute, "")
//                        .or().eq(WaybillDO::getWaybillRoute, "[]")
//                        .or().eq(WaybillDO::getWaybillRoute, "null"))
                .orderByAsc(WaybillDO::getOrderTime)
        );
    }

    /**
     * 处理单个运单更新
     */
    private WaybillUpdateRecord processWaybillUpdate(WaybillDO waybill, UpdateType updateType, boolean isPreview) {
        long startTime = System.currentTimeMillis();
        WaybillUpdateRecord record = new WaybillUpdateRecord(waybill.getWaybillNo(), updateType, isPreview);

        try {
            // 记录原始数据
            recordOriginalData(record, waybill);

            // 根据更新类型执行相应的更新操作
            switch (updateType) {
                case ROUTE_DATA:
                    updateRouteDataOnly(record, waybill, isPreview);
                    break;
                case LOGISTICS_STATUS:
                    updateLogisticsStatusOnly(record, waybill, isPreview);
                    break;
                case ACTUAL_WEIGHT:
                    updateActualWeightOnly(record, waybill, isPreview);
                    break;
                case COLLECT_TIME:
                    updateCollectTimeOnly(record, waybill, isPreview);
                    break;
                case RECEIVER_PROVINCE:
                    updateReceiverProvinceOnly(record, waybill, isPreview);
                    break;
                case PROVINCE_EXTRACT:
                    updateProvinceExtractOnly(record, waybill, isPreview);
                    break;
                case PRODUCT_MARKET_ID:
                    updateProductMarketIdOnly(record, waybill, isPreview);
                    break;
                case AMOUNT_DIFFERENCE:
                    updateAmountDifferenceOnly(record, waybill, isPreview);
                    break;
                case ALL:
                    updateAllFields(record, waybill, isPreview);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的更新类型：" + updateType);
            }

            record.success = true;

        } catch (Exception e) {
            record.success = false;
            record.errorMessage = e.getMessage();
            log.error("❌ 处理运单失败 - 运单号：{}, 错误：{}", waybill.getWaybillNo(), e.getMessage());
        } finally {
            long endTime = System.currentTimeMillis();
            record.processDuration = endTime - startTime;
        }

        return record;
    }

    /**
     * 记录原始数据
     */
    private void recordOriginalData(WaybillUpdateRecord record, WaybillDO waybill) {
        record.waybillId = waybill.getId();
        record.userId = waybill.getUserId();
        record.orderTime = waybill.getOrderTime();
        record.originalLogisticsStatus = waybill.getLogisticsStatus();
        record.originalLogisticsStatusName = getLogisticsStatusName(waybill.getLogisticsStatus());
        record.originalRouteData = waybill.getWaybillRoute();
        record.originalActualWeight = waybill.getActualWeight();
        record.originalCollectTime = waybill.getCollectTime();
        record.originalReceiverProvince = waybill.getReceiverProvince();
        record.originalReceiverAddress = waybill.getReceiverAddress();
        record.originalProductMarketId = waybill.getProductMarketId();
        record.originalSupplementAmount = waybill.getSupplementAmount();
        record.originalRefundAmount = waybill.getRefundAmount();
        record.originalEstimatedAmount = waybill.getEstimatedAmount();
        record.originalPaidAmount = waybill.getPaidAmount();
    }

    /**
     * 只更新轨迹数据 - 优先使用运单自身数据，轨迹为空时才获取
     */
    private void updateRouteDataOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            // 优先使用运单自身的轨迹数据
            if (StrUtil.isNotBlank(record.originalRouteData) && !isEmptyRouteData(record.originalRouteData)) {
                record.newRouteData = record.originalRouteData;
                log.debug("📋 使用运单自身轨迹数据 - 运单号：{}, 节点数：{}",
                    waybill.getWaybillNo(), countRouteNodes(record.originalRouteData));
                return;
            }

            // 轨迹数据为空时才拉取最新轨迹
            log.debug("📋 运单轨迹数据为空，拉取最新轨迹 - 运单号：{}", waybill.getWaybillNo());
            String routesResult = fetchRouteData(waybill.getWaybillNo());
            if (StrUtil.isBlank(routesResult)) {
                record.newRouteData = record.originalRouteData;
                return;
            }

            // 解析轨迹数据
            String newRouteData = parseAndUpdateRouteData(routesResult, record.originalRouteData);
            record.newRouteData = newRouteData;

            // 检查是否需要更新
            if (StrUtil.isNotBlank(newRouteData) && !newRouteData.equals(record.originalRouteData)) {
                if (!isPreview) {
                    // 真实更新数据库
                    WaybillDO updateWaybill = new WaybillDO();
                    updateWaybill.setId(waybill.getId());
                    updateWaybill.setWaybillRoute(newRouteData);
                    waybillMapper.updateById(updateWaybill);
                }
                log.debug("📋 从最新轨迹更新数据 - 运单号：{}, 节点数：{}", waybill.getWaybillNo(), countRouteNodes(newRouteData));
            }

        } catch (Exception e) {
            throw new RuntimeException("更新轨迹数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 只更新物流状态 - 优先使用运单自身数据，没有轨迹数据时才拉取
     */
    private void updateLogisticsStatusOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            // 检查是否有轨迹数据
            if (StrUtil.isNotBlank(waybill.getWaybillRoute()) &&
                !isEmptyRouteData(waybill.getWaybillRoute())) {
                // 从现有轨迹数据中解析物流状态
                Integer statusFromRoute = parseLogisticsStatusFromRouteData(waybill.getWaybillRoute());
                if (statusFromRoute != null) {
                    record.newLogisticsStatus = statusFromRoute;
                    record.newLogisticsStatusName = getLogisticsStatusName(statusFromRoute);

                    if (!isPreview) {
                        // 真实更新数据库
                        WaybillDO updateWaybill = new WaybillDO();
                        updateWaybill.setId(waybill.getId());
                        updateWaybill.setLogisticsStatus(statusFromRoute);
                        waybillMapper.updateById(updateWaybill);
                    }
                    log.debug("📦 从现有轨迹解析物流状态 - 运单号：{}, 状态：{}",
                        waybill.getWaybillNo(), record.newLogisticsStatusName);
                    return;
                }
            }

            // 没有轨迹数据时才拉取最新轨迹
            log.debug("📋 运单无轨迹数据，拉取最新轨迹 - 运单号：{}", waybill.getWaybillNo());
            String routesResult = fetchRouteData(waybill.getWaybillNo());
            if (StrUtil.isBlank(routesResult)) {
                record.newLogisticsStatus = record.originalLogisticsStatus;
                record.newLogisticsStatusName = record.originalLogisticsStatusName;
                return;
            }

            Integer newLogisticsStatus = parseLogisticsStatusFromRoutes(routesResult);
            record.newLogisticsStatus = newLogisticsStatus != null ? newLogisticsStatus : record.originalLogisticsStatus;
            record.newLogisticsStatusName = getLogisticsStatusName(record.newLogisticsStatus);

            // 检查是否需要更新
            if (newLogisticsStatus != null && !newLogisticsStatus.equals(record.originalLogisticsStatus)) {
                if (!isPreview) {
                    // 真实更新数据库
                    WaybillDO updateWaybill = new WaybillDO();
                    updateWaybill.setId(waybill.getId());
                    updateWaybill.setLogisticsStatus(newLogisticsStatus);
                    waybillMapper.updateById(updateWaybill);
                }
                log.debug("📦 从最新轨迹更新物流状态 - 运单号：{}, {} -> {}", waybill.getWaybillNo(),
                    record.originalLogisticsStatusName, record.newLogisticsStatusName);
            }

        } catch (Exception e) {
            throw new RuntimeException("更新物流状态失败：" + e.getMessage(), e);
        }
    }

    /**
     * 只更新真实重量 - 优先使用运单自身数据，重量为空时才获取
     */
    private void updateActualWeightOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            // 优先使用运单自身的真实重量
            if (record.originalActualWeight != null && record.originalActualWeight.compareTo(BigDecimal.ZERO) > 0) {
                record.newActualWeight = record.originalActualWeight;
                log.debug("⚖️ 使用运单自身真实重量 - 运单号：{}, 重量：{}kg",
                    waybill.getWaybillNo(), record.originalActualWeight);
                return;
            }

            BigDecimal newActualWeight = null;

            // 检查是否有轨迹数据，先从现有轨迹数据中解析重量
            if (StrUtil.isNotBlank(waybill.getWaybillRoute()) && !isEmptyRouteData(waybill.getWaybillRoute())) {
                newActualWeight = parseActualWeightFromRouteData(waybill.getWaybillRoute());
                if (newActualWeight != null && newActualWeight.compareTo(BigDecimal.ZERO) > 0) {
                    record.newActualWeight = newActualWeight;

                    if (!isPreview) {
                        // 真实更新数据库
                        WaybillDO updateWaybill = new WaybillDO();
                        updateWaybill.setId(waybill.getId());
                        updateWaybill.setActualWeight(newActualWeight);
                        waybillMapper.updateById(updateWaybill);
                    }
                    log.debug("⚖️ 从现有轨迹解析真实重量 - 运单号：{}, 重量：{}kg",
                        waybill.getWaybillNo(), newActualWeight);
                    return;
                }
            }

            // 真实重量为空且没有轨迹数据时才拉取最新轨迹
            log.debug("📋 运单真实重量为空，拉取最新轨迹 - 运单号：{}", waybill.getWaybillNo());
            String routesResult = fetchRouteData(waybill.getWaybillNo());
            newActualWeight = parseActualWeightFromRoutes(routesResult);

            // 如果API没有提供重量信息，保持原值
            record.newActualWeight = newActualWeight != null ? newActualWeight : record.originalActualWeight;

            // 如果有新的重量数据且与原值不同
            if (record.newActualWeight != null &&
                (record.originalActualWeight == null ||
                 record.newActualWeight.compareTo(record.originalActualWeight) != 0)) {

                if (!isPreview) {
                    // 真实更新数据库
                    WaybillDO updateWaybill = new WaybillDO();
                    updateWaybill.setId(waybill.getId());
                    updateWaybill.setActualWeight(record.newActualWeight);
                    waybillMapper.updateById(updateWaybill);
                }
                log.debug("⚖️ 从最新轨迹更新真实重量 - 运单号：{}, {}kg -> {}kg", waybill.getWaybillNo(),
                    record.originalActualWeight, record.newActualWeight);
            }

        } catch (Exception e) {
            throw new RuntimeException("更新真实重量失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从现有轨迹数据中解析真实重量
     */
    private BigDecimal parseActualWeightFromRouteData(String routeData) {
        try {
            if (isEmptyRouteData(routeData)) {
                return null;
            }

            JSONArray routes = JSON.parseArray(routeData);
            if (routes == null || routes.isEmpty()) {
                return null;
            }

            // 查找包含重量信息的轨迹记录
            for (int i = 0; i < routes.size(); i++) {
                JSONObject route = routes.getJSONObject(i);

                // 尝试从轨迹记录中获取重量信息（具体字段名需要根据实际数据确定）
                String weightStr = route.getString("weight");
                if (StrUtil.isNotBlank(weightStr)) {
                    try {
                        return new BigDecimal(weightStr);
                    } catch (NumberFormatException e) {
                        log.debug("⚠️ 重量格式错误：{}", weightStr);
                    }
                }

                // 也可能在remark或context字段中包含重量信息
                String remark = route.getString("remark");
                if (StrUtil.isNotBlank(remark)) {
                    BigDecimal weightFromRemark = extractWeightFromText(remark);
                    if (weightFromRemark != null) {
                        return weightFromRemark;
                    }
                }
            }

            return null;

        } catch (Exception e) {
            log.debug("⚠️ 从轨迹数据解析真实重量失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 从轨迹数据中解析真实重量
     */
    private BigDecimal parseActualWeightFromRoutes(String routesResult) {
        try {
            if (StrUtil.isBlank(routesResult)) {
                return null;
            }

            JSONObject routesJson = JSON.parseObject(routesResult);
            if (routesJson == null || !"A1000".equals(routesJson.getString("apiResultCode"))) {
                return null;
            }

            JSONObject apiResultData = routesJson.getJSONObject("apiResultData");
            if (apiResultData == null) return null;

            JSONObject msgData = apiResultData.getJSONObject("msgData");
            if (msgData == null) return null;

            JSONArray routeResps = msgData.getJSONArray("routeResps");
            if (routeResps == null || routeResps.isEmpty()) return null;

            JSONObject routeResp = routeResps.getJSONObject(0);

            // 尝试从API响应中获取重量信息（具体字段名需要根据实际API确定）
            String weightStr = routeResp.getString("weight");
            if (StrUtil.isNotBlank(weightStr)) {
                return new BigDecimal(weightStr);
            }

            // 如果顶层没有重量信息，从routes数组中查找
            JSONArray routes = routeResp.getJSONArray("routes");
            if (routes != null && !routes.isEmpty()) {
                String routeDataStr = routes.toJSONString();
                return parseActualWeightFromRouteData(routeDataStr);
            }

            return null;

        } catch (Exception e) {
            log.debug("⚠️ 解析真实重量失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 从文本中提取重量信息
     */
    private BigDecimal extractWeightFromText(String text) {
        if (StrUtil.isBlank(text)) {
            return null;
        }

        try {
            // 使用正则表达式匹配重量信息，如 "重量：1.5kg" 或 "1.5公斤"
            String[] patterns = {
                "重量[：:](\\d+\\.?\\d*)k?g?",
                "重量[：:](\\d+\\.?\\d*)公斤",
                "(\\d+\\.?\\d*)k?g",
                "(\\d+\\.?\\d*)公斤"
            };

            for (String pattern : patterns) {
                java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
                java.util.regex.Matcher m = p.matcher(text);
                if (m.find()) {
                    String weightStr = m.group(1);
                    return new BigDecimal(weightStr);
                }
            }

            return null;

        } catch (Exception e) {
            log.debug("⚠️ 从文本提取重量失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 只更新揽收时间 - 优先使用运单自身数据，没有轨迹数据时才拉取
     */
    private void updateCollectTimeOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            LocalDateTime newCollectTime = null;

            // 检查是否有轨迹数据
            if (StrUtil.isNotBlank(waybill.getWaybillRoute()) &&
                !isEmptyRouteData(waybill.getWaybillRoute())) {
                // 从现有轨迹数据中解析揽收时间
                newCollectTime = parseCollectTimeFromRouteData(waybill.getWaybillRoute());
                if (newCollectTime != null) {
                    record.newCollectTime = newCollectTime;

                    if (!isPreview) {
                        // 真实更新数据库
                        WaybillDO updateWaybill = new WaybillDO();
                        updateWaybill.setId(waybill.getId());
                        updateWaybill.setCollectTime(newCollectTime);
                        waybillMapper.updateById(updateWaybill);
                    }
                    log.debug("📅 从现有轨迹解析揽收时间 - 运单号：{}, 时间：{}",
                        waybill.getWaybillNo(), newCollectTime);
                    return;
                }
            }

            // 没有轨迹数据时才拉取最新轨迹
            log.debug("📋 运单无轨迹数据，拉取最新轨迹 - 运单号：{}", waybill.getWaybillNo());
            String routesResult = fetchRouteData(waybill.getWaybillNo());
            newCollectTime = parseCollectTimeFromRoutes(routesResult);

            // 如果API没有提供揽收时间，保持原值
            record.newCollectTime = newCollectTime != null ? newCollectTime : record.originalCollectTime;

            // 如果有新的揽收时间且与原值不同
            if (record.newCollectTime != null && !record.newCollectTime.equals(record.originalCollectTime)) {
                if (!isPreview) {
                    // 真实更新数据库
                    WaybillDO updateWaybill = new WaybillDO();
                    updateWaybill.setId(waybill.getId());
                    updateWaybill.setCollectTime(record.newCollectTime);
                    waybillMapper.updateById(updateWaybill);
                }
                log.debug("📅 从最新轨迹更新揽收时间 - 运单号：{}, {} -> {}", waybill.getWaybillNo(),
                    record.originalCollectTime, record.newCollectTime);
            }

        } catch (Exception e) {
            throw new RuntimeException("更新揽收时间失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从轨迹数据中解析揽收时间
     */
    private LocalDateTime parseCollectTimeFromRoutes(String routesResult) {
        try {
            if (StrUtil.isBlank(routesResult)) {
                return null;
            }

            JSONObject routesJson = JSON.parseObject(routesResult);
            if (routesJson == null || !"A1000".equals(routesJson.getString("apiResultCode"))) {
                return null;
            }

            JSONObject apiResultData = routesJson.getJSONObject("apiResultData");
            if (apiResultData == null) return null;

            JSONObject msgData = apiResultData.getJSONObject("msgData");
            if (msgData == null) return null;

            JSONArray routeResps = msgData.getJSONArray("routeResps");
            if (routeResps == null || routeResps.isEmpty()) return null;

            JSONObject routeResp = routeResps.getJSONObject(0);
            JSONArray routes = routeResp.getJSONArray("routes");
            if (routes == null || routes.isEmpty()) return null;

            // 查找揽收时间（opCode为50的记录）
            for (int i = 0; i < routes.size(); i++) {
                JSONObject route = routes.getJSONObject(i);
                String opCode = route.getString("opCode");
                if ("50".equals(opCode)) { // 已收件
                    String acceptTimeStr = route.getString("acceptTime");
                    if (StrUtil.isNotBlank(acceptTimeStr)) {
                        // 解析时间字符串，格式可能需要根据实际API调整
                        return LocalDateTime.parse(acceptTimeStr.replace(" ", "T"));
                    }
                }
            }

            return null;

        } catch (Exception e) {
            log.debug("⚠️ 解析揽收时间失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 只更新收件省份 - 参考 WalletSyncTest 的逻辑
     */
    private void updateReceiverProvinceOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            String receiverProvince = record.originalReceiverProvince;

            // 优先使用运单自身的收件省份
            if (StrUtil.isNotBlank(receiverProvince)) {
                record.newReceiverProvince = receiverProvince;
                log.debug("🏠 使用运单自身收件省份 - 运单号：{}, 省份：{}",
                    waybill.getWaybillNo(), receiverProvince);
                return;
            }

            // 收件人省份为空时，尝试从收件人地址中提取 - 参考 WalletSyncTest 逻辑
            if (StrUtil.isBlank(receiverProvince)) {
                log.debug("🏠 收件人省份为空 - 运单号：{}", waybill.getWaybillNo());
                String receiverAddress = waybill.getReceiverAddress();

                // 首先尝试使用系统的地址解析工具
                receiverProvince = AddressValidatorUtil.getProvince(receiverAddress);

                // 如果系统工具无法解析，使用新版自左向右逐词匹配方法
                if (StrUtil.isBlank(receiverProvince)) {
                    receiverProvince = AddressProvinceExtractor.extractProvince(receiverAddress);
                    log.debug("🔧 使用分轮次匹配方法提取省份 - 运单号：{}, 地址：{}, 省份：{}",
                        waybill.getWaybillNo(), receiverAddress, receiverProvince);
                } else {
                    log.debug("🏠 使用系统工具提取省份 - 运单号：{}, 省份：{}",
                        waybill.getWaybillNo(), receiverProvince);
                }

                record.newReceiverProvince = receiverProvince;

                if (StrUtil.isNotBlank(receiverProvince)) {
                    if (!isPreview) {
                        // 真实更新数据库
                        WaybillDO updateWaybill = new WaybillDO();
                        updateWaybill.setId(waybill.getId());
                        updateWaybill.setReceiverProvince(receiverProvince);
                        waybillMapper.updateById(updateWaybill);
                    }
                    log.debug("🏠 从地址提取并更新收件人省份 - 运单号：{}, 省份：{}",
                        waybill.getWaybillNo(), receiverProvince);
                } else {
                    log.debug("⚠️ 无法从地址提取省份信息 - 运单号：{}, 地址：{}",
                        waybill.getWaybillNo(), receiverAddress);
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("更新收件省份失败：" + e.getMessage(), e);
        }
    }

    /**
     * 只更新省份提取 - 使用 AddressProvinceExtractor.extractProvince 方法
     */
    private void updateProvinceExtractOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            String receiverAddress = waybill.getReceiverAddress();
            String originalProvince = record.originalReceiverProvince;

            log.debug("🏠 开始省份提取 - 运单号：{}, 原省份：{}, 地址：{}",
                waybill.getWaybillNo(), originalProvince, receiverAddress);

            // 使用 AddressProvinceExtractor.extractProvince 提取省份
            String extractedProvince = null;
            if (StrUtil.isNotBlank(receiverAddress)) {
                extractedProvince = AddressProvinceExtractor.extractProvince(receiverAddress);
                log.debug("🔧 使用 AddressProvinceExtractor 提取省份 - 运单号：{}, 地址：{}, 提取结果：{}",
                    waybill.getWaybillNo(), receiverAddress, extractedProvince);
            }

            record.newReceiverProvince = extractedProvince;

            if (StrUtil.isNotBlank(extractedProvince)) {
                if (!isPreview) {
                    // 真实更新数据库
                    WaybillDO updateWaybill = new WaybillDO();
                    updateWaybill.setId(waybill.getId());
                    updateWaybill.setReceiverProvince(extractedProvince);
                    waybillMapper.updateById(updateWaybill);
                }
                log.debug("🏠 省份提取并更新成功 - 运单号：{}, 提取省份：{}",
                    waybill.getWaybillNo(), extractedProvince);
            } else {
                log.debug("⚠️ 无法从地址提取省份信息 - 运单号：{}, 地址：{}",
                    waybill.getWaybillNo(), receiverAddress);
            }

        } catch (Exception e) {
            throw new RuntimeException("省份提取失败：" + e.getMessage(), e);
        }
    }

    /**
     * 只更新市场产品ID - 参考 WalletSyncTest 的逻辑
     */
    private void updateProductMarketIdOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            Long productMarketId = record.originalProductMarketId;

            // 优先使用运单自身的市场产品ID
            if (productMarketId != null) {
                record.newProductMarketId = productMarketId;
                log.debug("🏪 使用运单自身市场产品ID - 运单号：{}, ID：{}",
                    waybill.getWaybillNo(), productMarketId);
                return;
            }

            // 产品市场ID为空时，尝试从产品名称和市场ID获取 - 完全参考 WalletSyncTest 逻辑
            if (productMarketId == null) {
                log.debug("🏪 产品市场ID为空 - 运单号：{}", waybill.getWaybillNo());

                String productName = waybill.getProductName();
                if (StrUtil.isNotBlank(productName)) {
                    Long marketId = waybill.getMarketId();
                    Map detail = productService.getOneByProductNameAndMarketId(productName, marketId);
                    if (detail != null) {
                        productMarketId = (Long) detail.get("productMarketId");
                        record.newProductMarketId = productMarketId;

                        if (!isPreview) {
                            // 真实更新数据库
                            WaybillDO updateWaybill = new WaybillDO();
                            updateWaybill.setId(waybill.getId());
                            updateWaybill.setProductMarketId(productMarketId);
                            waybillMapper.updateById(updateWaybill);
                        }
                        log.debug("🏪 从产品名称和市场ID获取并更新产品市场ID - 运单号：{}, 产品名称：{}, 市场ID：{}, 产品市场ID：{}",
                            waybill.getWaybillNo(), productName, marketId, productMarketId);
                    } else {
                        log.debug("⚠️ 无法从产品名称和市场ID获取产品市场ID - 运单号：{}, 产品名称：{}, 市场ID：{}",
                            waybill.getWaybillNo(), productName, marketId);
                        record.newProductMarketId = null;
                    }
                } else {
                    log.debug("⚠️ 产品名称为空 - 运单号：{}", waybill.getWaybillNo());
                    record.newProductMarketId = null;
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("更新市场产品ID失败：" + e.getMessage(), e);
        }
    }

    /**
     * 只更新差额补扣费/退款 - 参考 WalletSyncTest 的实现
     */
    private void updateAmountDifferenceOnly(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        try {
            // 计算新的金额
            calculateAmounts(record, waybill);

            // 修订的费用计算规则：
            // 费用差额 = 计算应收金额 - (原已付金额 + 原退款金额 + 原补扣金额)
            Integer originalPaidAmount = record.originalPaidAmount != null ? record.originalPaidAmount : 0;
            Integer originalRefundAmount = record.originalRefundAmount != null ? record.originalRefundAmount : 0;
            Integer originalSupplementAmount = record.originalSupplementAmount != null ? record.originalSupplementAmount : 0;

            Integer totalOriginalAmount = originalPaidAmount + originalRefundAmount + originalSupplementAmount;
            record.amountDifference = record.calculatedReceivableAmount.intValue() - totalOriginalAmount;

            log.debug("💰 费用计算详情 - 运单号：{}", waybill.getWaybillNo());
            log.debug("   计算应收金额：{}分", record.calculatedReceivableAmount.intValue());
            log.debug("   原已付金额：{}分", originalPaidAmount);
            log.debug("   原退款金额：{}分", originalRefundAmount);
            log.debug("   原补扣金额：{}分", originalSupplementAmount);
            log.debug("   原总金额：{}分", totalOriginalAmount);
            log.debug("   费用差额：{}分", record.amountDifference);

            // 记录钱包操作前余额
            if (waybill.getUserId() != null) {
                PayWalletDO wallet = payWalletService.getOrCreateWallet(waybill.getUserId(), 1);
                record.walletBalanceBefore = new BigDecimal(wallet.getBalance()).divide(new BigDecimal(100));

                // 执行钱包操作 - 修订的费用计算规则
                if (record.amountDifference > 0) {
                    // 费用差额 > 0，需要补扣，补扣金额为费用差额
                    record.walletOperationType = "补扣";
                    record.newSupplementAmount = (record.originalSupplementAmount != null ? record.originalSupplementAmount : 0) + record.amountDifference;
                    record.newRefundAmount = record.originalRefundAmount;

                    if (!isPreview) {
                        // 注意：补扣费为减少余额，所以接口传递时差额需要转正数（reduceWalletBalance接口内部会处理为负数）
                        PayWalletTransactionDO transaction = payWalletService.reduceWalletBalance(
                            wallet.getId(), waybill.getWaybillNo(), PayWalletBizTypeEnum.SUPPLEMENTARY_DEDUCTION,
                            record.amountDifference, "14日钱包同步后-7月1日0点~14日5点老系统运单补扣运费");
                        record.walletTransactionNo = transaction.getNo();

                        // 更新运单补扣金额
                        WaybillDO updateWaybill = new WaybillDO();
                        updateWaybill.setId(waybill.getId());
                        updateWaybill.setSupplementAmount(record.newSupplementAmount);
                        waybillMapper.updateById(updateWaybill);

                        // 获取操作后余额
                        PayWalletDO updatedWallet = payWalletMapper.selectById(wallet.getId());
                        record.walletBalanceAfter = new BigDecimal(updatedWallet.getBalance()).divide(new BigDecimal(100));

                        log.debug("💸 执行补扣操作 - 运单号：{}, 补扣金额：{}分, 流水号：{}, 余额：{}→{}元",
                                waybill.getWaybillNo(), record.amountDifference, transaction.getNo(),
                                record.walletBalanceBefore, record.walletBalanceAfter);
                    } else {
                        record.walletBalanceAfter = record.walletBalanceBefore.subtract(new BigDecimal(record.amountDifference).divide(new BigDecimal(100)));
                        log.debug("🔍 预览补扣操作 - 运单号：{}, 补扣金额：{}分, 当前余额：{}元",
                                waybill.getWaybillNo(), record.amountDifference, record.walletBalanceBefore);
                    }

                } else if (record.amountDifference < 0) {
                    // 费用差额 < 0，需要退款，退款金额为费用差额的绝对值
                    Integer refundAmount = Math.abs(record.amountDifference);
                    record.walletOperationType = "退款";
                    record.newSupplementAmount = record.originalSupplementAmount;
                    record.newRefundAmount = (record.originalRefundAmount != null ? record.originalRefundAmount : 0) + refundAmount;

                    if (!isPreview) {
                        // 注意：退款为增加余额，所以接口传递时需要传正数（addWalletBalance接口内部会处理为正数）
                        PayWalletTransactionDO transaction = payWalletService.addWalletBalance(
                            wallet.getId(), waybill.getWaybillNo(), PayWalletBizTypeEnum.PAYMENT_REFUND,
                            refundAmount, "14日钱包同步后-7月1日0点~14日5点老系统运单退款");
                        record.walletTransactionNo = transaction.getNo();

                        // 更新运单退款金额
                        WaybillDO updateWaybill = new WaybillDO();
                        updateWaybill.setId(waybill.getId());
                        updateWaybill.setRefundAmount(record.newRefundAmount);
                        waybillMapper.updateById(updateWaybill);

                        // 获取操作后余额
                        PayWalletDO updatedWallet = payWalletMapper.selectById(wallet.getId());
                        record.walletBalanceAfter = new BigDecimal(updatedWallet.getBalance()).divide(new BigDecimal(100));

                        log.debug("💰 执行退款操作 - 运单号：{}, 退款金额：{}分, 流水号：{}, 余额：{}→{}元",
                                waybill.getWaybillNo(), refundAmount, transaction.getNo(),
                                record.walletBalanceBefore, record.walletBalanceAfter);
                    } else {
                        record.walletBalanceAfter = record.walletBalanceBefore.add(new BigDecimal(refundAmount).divide(new BigDecimal(100)));
                        log.debug("🔍 预览退款操作 - 运单号：{}, 退款金额：{}分, 当前余额：{}元",
                                waybill.getWaybillNo(), refundAmount, record.walletBalanceBefore);
                    }

                } else {
                    // 费用差额 = 0，无需操作
                    record.walletOperationType = "无操作";
                    record.newSupplementAmount = record.originalSupplementAmount;
                    record.newRefundAmount = record.originalRefundAmount;
                    record.walletBalanceAfter = record.walletBalanceBefore;

                    log.debug("✅ 无需操作 - 运单号：{}, 费用差额为0, 当前余额：{}元",
                            waybill.getWaybillNo(), record.walletBalanceBefore);
                }

                log.debug("💰 差额补扣费/退款更新完成 - 运单号：{}", waybill.getWaybillNo());
                log.debug("   新应收：{}分, 原总金额：{}分, 费用差额：{}分, 操作：{}",
                    record.calculatedReceivableAmount.intValue(), totalOriginalAmount,
                    record.amountDifference, record.walletOperationType);
            }

        } catch (Exception e) {
            throw new RuntimeException("更新差额补扣费/退款失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新所有字段
     */
    private void updateAllFields(WaybillUpdateRecord record, WaybillDO waybill, boolean isPreview) {
        // 按顺序执行所有更新操作
        updateRouteDataOnly(record, waybill, isPreview);
        updateLogisticsStatusOnly(record, waybill, isPreview);
        updateActualWeightOnly(record, waybill, isPreview);
        updateCollectTimeOnly(record, waybill, isPreview);
        updateReceiverProvinceOnly(record, waybill, isPreview);
        updateProvinceExtractOnly(record, waybill, isPreview);
        updateProductMarketIdOnly(record, waybill, isPreview);
//        updateAmountDifferenceOnly(record, waybill, isPreview);
    }

    /**
     * 计算运单金额 - 参考 WalletSyncTest 的实现
     */
    private void calculateAmounts(WaybillUpdateRecord record, WaybillDO waybill) {
        try {
            String receiverProvince = waybill.getReceiverProvince();
            Long productMarketId = waybill.getProductMarketId();
            BigDecimal actualWeight = waybill.getActualWeight();

            // 参数验证 - 参考 WalletSyncTest 的验证逻辑
            if (StrUtil.isBlank(receiverProvince)) {
                throw new RuntimeException("收件人省份为空");
            }

            if (productMarketId == null) {
                throw new RuntimeException("产品市场ID为空");
            }

            if (actualWeight == null || actualWeight.compareTo(BigDecimal.ZERO) <= 0) {
                throw new RuntimeException("实际重量为空或小于等于0");
            }

            log.debug("💰 开始计算金额 - 运单号：{}, 省份：{}, 产品市场ID：{}, 重量：{}kg",
                    waybill.getWaybillNo(), receiverProvince, productMarketId, actualWeight);

            // 计算应收金额 - 完全参考 WalletSyncTest 的调用方式
            BigDecimal receivableAmountDecimal = shippingTemplateService.getFeeByProductMarketIdAndProvinceAndType(
                    receiverProvince, String.valueOf(productMarketId), actualWeight,
                    ShippingTemplateTypeEnum.RECEIVABLE.getType(), null, null);
            record.calculatedReceivableAmount = receivableAmountDecimal.multiply(new BigDecimal(100)); // 转换为分

            // 计算应付金额 - 完全参考 WalletSyncTest 的调用方式
            BigDecimal payableAmountDecimal = shippingTemplateService.getFeeByProductMarketIdAndProvinceAndType(
                    receiverProvince, String.valueOf(productMarketId), actualWeight,
                    ShippingTemplateTypeEnum.PAYABLE.getType(), null, null);
            record.calculatedPayableAmount = payableAmountDecimal.multiply(new BigDecimal(100)); // 转换为分

            log.debug("💰 金额计算完成 - 运单号：{}, 应收：{}分, 应付：{}分",
                    waybill.getWaybillNo(), record.calculatedReceivableAmount, record.calculatedPayableAmount);

        } catch (Exception e) {
            log.error("❌ 金额计算失败 - 运单号：{}, 省份：{}, 产品市场ID：{}, 重量：{}kg, 错误：{}",
                    waybill.getWaybillNo(), waybill.getReceiverProvince(), waybill.getProductMarketId(),
                    waybill.getActualWeight(), e.getMessage());
            throw new RuntimeException("金额计算失败：" + e.getMessage(), e);
        }
    }

    /**
     * 拉取轨迹数据 - 参考 WalletSyncTest 的实现
     */
    private String fetchRouteData(String waybillNo) {
        try {
            log.debug("🔍 拉取物流轨迹 - 运单号：{}", waybillNo);
            String routesResult = ExpressAPIUtil.searchRoutes(waybillNo);

            if (StrUtil.isBlank(routesResult)) {
                log.debug("⚠️ 未获取到轨迹数据 - 运单号：{}", waybillNo);
                return null;
            }

            return routesResult;

        } catch (Exception e) {
            log.warn("⚠️ 拉取轨迹数据异常 - 运单号：{}, 错误：{}", waybillNo, e.getMessage());
            return null;
        }
    }

    /**
     * 解析并更新轨迹数据 - 参考 WalletSyncTest 的实现
     */
    private String parseAndUpdateRouteData(String routesResult, String existingRouteData) {
        try {
            JSONObject routesJson = JSON.parseObject(routesResult);
            if (routesJson == null || !"A1000".equals(routesJson.getString("apiResultCode"))) {
                return existingRouteData;
            }

            JSONObject apiResultData = routesJson.getJSONObject("apiResultData");
            if (apiResultData == null) {
                return existingRouteData;
            }

            JSONObject msgData = apiResultData.getJSONObject("msgData");
            if (msgData == null) {
                return existingRouteData;
            }

            JSONArray routeResps = msgData.getJSONArray("routeResps");
            if (routeResps == null || routeResps.isEmpty()) {
                return existingRouteData;
            }

            JSONObject routeResp = routeResps.getJSONObject(0);
            JSONArray routes = routeResp.getJSONArray("routes");
            if (routes == null || routes.isEmpty()) {
                return existingRouteData;
            }

            // 直接返回routes数组的JSON字符串
            String newRouteData = routes.toJSONString();

            // 只有当新数据与现有数据不同时才返回新数据
            if (!newRouteData.equals(existingRouteData)) {
                log.debug("📋 轨迹数据已更新，节点数量：{}", routes.size());
                return newRouteData;
            }

            return existingRouteData;

        } catch (Exception e) {
            log.warn("⚠️ 解析轨迹数据失败：{}", e.getMessage());
            return existingRouteData;
        }
    }

    /**
     * 从轨迹结果中解析物流状态 - 参考 WalletSyncTest 的实现
     */
    private Integer parseLogisticsStatusFromRoutes(String routesResult) {
        try {
            JSONObject routesJson = JSON.parseObject(routesResult);
            if (routesJson == null || !"A1000".equals(routesJson.getString("apiResultCode"))) {
                return null;
            }

            JSONObject apiResultData = routesJson.getJSONObject("apiResultData");
            if (apiResultData == null) {
                return null;
            }

            JSONObject msgData = apiResultData.getJSONObject("msgData");
            if (msgData == null) {
                return null;
            }

            JSONArray routeResps = msgData.getJSONArray("routeResps");
            if (routeResps == null || routeResps.isEmpty()) {
                return null;
            }

            JSONObject routeResp = routeResps.getJSONObject(0);
            JSONArray routes = routeResp.getJSONArray("routes");
            if (routes == null || routes.isEmpty()) {
                return null;
            }

            // 获取最新的轨迹记录（最后一条是最新的）
            JSONObject latestRoute = routes.getJSONObject(routes.size() - 1);
            String opCode = latestRoute.getString("opCode");

            // 根据opCode转换为物流状态
            return convertOpCodeToLogisticsStatus(opCode);

        } catch (Exception e) {
            log.warn("⚠️ 解析物流状态失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 将opCode转换为物流状态
     */
    private Integer convertOpCodeToLogisticsStatus(String opCode) {
        if (StrUtil.isBlank(opCode)) {
            return null;
        }

        switch (opCode) {
            case "50": // 已收件
                return LogisticsStatusEnum.COLLECTED.getStatus();
            case "10": case "11": case "14": case "15": case "16": case "17": case "18":
            case "30": case "31": // 运输中
                return LogisticsStatusEnum.IN_TRANSIT.getStatus();
            case "40": case "44": // 派件中
                return LogisticsStatusEnum.OUT_FOR_DELIVERY.getStatus();
            case "80": // 已签收
                return LogisticsStatusEnum.DELIVERED.getStatus();
            case "33": case "35": case "36": case "37": case "38": case "39": // 异常件
                return LogisticsStatusEnum.EXCEPTION.getStatus();
            case "99": // 已消单
                return LogisticsStatusEnum.CANCELLED.getStatus();
            case "81": case "82": case "83": case "84": case "85": case "86": case "87": // 已退回
                return LogisticsStatusEnum.RETURNED.getStatus();
            default:
                return null;
        }
    }

    /**
     * 计算轨迹节点数量
     */
    private int countRouteNodes(String routeData) {
        if (StrUtil.isBlank(routeData)) {
            return 0;
        }

        try {
            JSONArray routes = JSON.parseArray(routeData);
            return routes != null ? routes.size() : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 检查轨迹数据是否为空
     */
    private boolean isEmptyRouteData(String routeData) {
        if (StrUtil.isBlank(routeData)) {
            return true;
        }

        // 检查常见的空值情况
        if ("[]".equals(routeData.trim()) || "null".equals(routeData.trim()) || "{}".equals(routeData.trim())) {
            return true;
        }

        try {
            JSONArray routes = JSON.parseArray(routeData);
            return routes == null || routes.isEmpty();
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 从现有轨迹数据中解析物流状态
     */
    private Integer parseLogisticsStatusFromRouteData(String routeData) {
        try {
            if (isEmptyRouteData(routeData)) {
                return null;
            }

            JSONArray routes = JSON.parseArray(routeData);
            if (routes == null || routes.isEmpty()) {
                return null;
            }

            // 获取最新的轨迹记录（最后一条是最新的）
            JSONObject latestRoute = routes.getJSONObject(routes.size() - 1);
            String opCode = latestRoute.getString("opCode");

            return convertOpCodeToLogisticsStatus(opCode);

        } catch (Exception e) {
            log.debug("⚠️ 从轨迹数据解析物流状态失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 从现有轨迹数据中解析揽收时间
     */
    private LocalDateTime parseCollectTimeFromRouteData(String routeData) {
        try {
            if (isEmptyRouteData(routeData)) {
                return null;
            }

            JSONArray routes = JSON.parseArray(routeData);
            if (routes == null || routes.isEmpty()) {
                return null;
            }

            // 查找揽收时间（opCode为50的记录）
            for (int i = 0; i < routes.size(); i++) {
                JSONObject route = routes.getJSONObject(i);
                String opCode = route.getString("opCode");
                if ("50".equals(opCode)) { // 已收件
                    String acceptTimeStr = route.getString("acceptTime");
                    if (StrUtil.isNotBlank(acceptTimeStr)) {
                        // 解析时间字符串，格式可能需要根据实际API调整
                        return LocalDateTime.parse(acceptTimeStr.replace(" ", "T"));
                    }
                }
            }

            return null;

        } catch (Exception e) {
            log.debug("⚠️ 从轨迹数据解析揽收时间失败：{}", e.getMessage());
            return null;
        }
    }



    /**
     * 获取物流状态名称
     */
    private String getLogisticsStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        LogisticsStatusEnum statusEnum = LogisticsStatusEnum.getByStatus(status);
        return statusEnum != null ? statusEnum.getName() : "未知状态(" + status + ")";
    }


    /**
     * 更新统计计数器
     */
    private void updateStatisticsCounters(BatchUpdateStatistics statistics, WaybillUpdateRecord record) {
        // 根据更新的字段类型增加相应计数器
        if (record.newRouteData != null && !record.newRouteData.equals(record.originalRouteData)) {
            statistics.routeUpdateCount++;
        }
        if (record.newLogisticsStatus != null && !record.newLogisticsStatus.equals(record.originalLogisticsStatus)) {
            statistics.statusUpdateCount++;
        }
        if (record.newActualWeight != null &&
            (record.originalActualWeight == null || record.newActualWeight.compareTo(record.originalActualWeight) != 0)) {
            statistics.weightUpdateCount++;
        }
        if (record.newCollectTime != null && !record.newCollectTime.equals(record.originalCollectTime)) {
            statistics.collectTimeUpdateCount++;
        }
        if (StrUtil.isNotBlank(record.newReceiverProvince) && !record.newReceiverProvince.equals(record.originalReceiverProvince)) {
            statistics.provinceUpdateCount++;
        }
        if (record.newProductMarketId != null && !record.newProductMarketId.equals(record.originalProductMarketId)) {
            statistics.productMarketUpdateCount++;
        }

        // 钱包操作统计
        if ("补扣".equals(record.walletOperationType)) {
            statistics.supplementCount++;
            statistics.totalSupplementAmount = statistics.totalSupplementAmount.add(
                new BigDecimal(Math.abs(record.amountDifference)).divide(new BigDecimal(100)));
        } else if ("退款".equals(record.walletOperationType)) {
            statistics.refundCount++;
            statistics.totalRefundAmount = statistics.totalRefundAmount.add(
                new BigDecimal(Math.abs(record.amountDifference)).divide(new BigDecimal(100)));
        }
    }

    /**
     * 打印批量统计信息
     */
    private void printBatchStatistics(BatchUpdateStatistics statistics, UpdateType updateType,
                                    boolean isPreview, long totalTime) {
        String operationType = isPreview ? "预览" : "真实入库";

        log.info("🎉 ========== {} - {} 统计信息 ==========", updateType.getDescription(), operationType);
        log.info("📊 总处理数量：{}", statistics.totalCount);
        log.info("✅ 成功数量：{}", statistics.successCount);
        log.info("❌ 失败数量：{}", statistics.failCount);

        if (statistics.routeUpdateCount > 0) {
            log.info("📋 轨迹更新数量：{}", statistics.routeUpdateCount);
        }
        if (statistics.statusUpdateCount > 0) {
            log.info("📦 状态更新数量：{}", statistics.statusUpdateCount);
        }
        if (statistics.weightUpdateCount > 0) {
            log.info("⚖️ 重量更新数量：{}", statistics.weightUpdateCount);
        }
        if (statistics.collectTimeUpdateCount > 0) {
            log.info("📅 揽收时间更新数量：{}", statistics.collectTimeUpdateCount);
        }
        if (statistics.provinceUpdateCount > 0) {
            log.info("🏠 省份更新数量：{}", statistics.provinceUpdateCount);
        }
        if (statistics.productMarketUpdateCount > 0) {
            log.info("🏪 产品市场更新数量：{}", statistics.productMarketUpdateCount);
        }
        if (statistics.supplementCount > 0) {
            log.info("💸 补扣操作数量：{}", statistics.supplementCount);
            log.info("💸 总补扣金额：{}元", statistics.totalSupplementAmount);
        }
        if (statistics.refundCount > 0) {
            log.info("💰 退款操作数量：{}", statistics.refundCount);
            log.info("💰 总退款金额：{}元", statistics.totalRefundAmount);
        }

        log.info("⏱️ 总处理时间：{}秒", totalTime / 1000.0);
        log.info("⚡ 平均处理时间：{}毫秒/单", statistics.totalCount > 0 ? statistics.totalProcessTime / statistics.totalCount : 0);

        if (!statistics.errorMessages.isEmpty()) {
            log.info("❌ 错误信息（前10条）：");
            statistics.errorMessages.stream().limit(10).forEach(msg -> log.info("   {}", msg));
        }

        log.info("🎉 =======================================");
    }

    /**
     * 保存批量更新记录到CSV文件
     */
    private void saveBatchUpdateRecords(BatchUpdateStatistics statistics, UpdateType updateType, boolean isPreview) {
        // 如果是省份提取类型，使用专门的CSV格式
        if (updateType == UpdateType.PROVINCE_EXTRACT) {
            saveProvinceExtractRecords(statistics, isPreview);
            return;
        }

        String operationType = isPreview ? "preview" : "real";
        String fileName = String.format("concurrent_waybill_update_%s_%s_%s.csv",
                updateType.name().toLowerCase(), operationType,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(fileName), "UTF-8"))) {

            // 写入CSV头部
            writer.write("运单号,运单ID,用户ID,下单时间,原物流状态,原物流状态名称,原轨迹节点数,原真实重量,原揽收时间,原收件省份,原收件地址,原产品市场ID,原补扣金额,原退款金额,原预估金额,原已付金额," +
                        "新物流状态,新物流状态名称,新轨迹节点数,新真实重量,新揽收时间,新收件省份,新产品市场ID,新补扣金额,新退款金额," +
                        "计算应收金额,计算应付金额,差额,钱包操作类型,钱包流水号,操作前余额,操作后余额," +
                        "是否成功,错误信息,更新类型,操作模式,处理时间,处理耗时,处理线程");
            writer.newLine();

            // 写入数据
            for (WaybillUpdateRecord record : statistics.records) {
                writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s",
                        record.waybillNo != null ? record.waybillNo : "",
                        record.waybillId != null ? record.waybillId : "",
                        record.userId != null ? record.userId : "",
                        record.orderTime != null ? record.orderTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "",
                        record.originalLogisticsStatus != null ? record.originalLogisticsStatus : "",
                        record.originalLogisticsStatusName != null ? record.originalLogisticsStatusName.replace(",", "，") : "",
                        countRouteNodes(record.originalRouteData),
                        record.originalActualWeight != null ? record.originalActualWeight : "",
                        record.originalCollectTime != null ? record.originalCollectTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "",
                        record.originalReceiverProvince != null ? record.originalReceiverProvince.replace(",", "，") : "",
                        record.originalReceiverAddress != null ? record.originalReceiverAddress.replace(",", "，") : "",
                        record.originalProductMarketId != null ? record.originalProductMarketId : "",
                        record.originalSupplementAmount != null ? record.originalSupplementAmount : "",
                        record.originalRefundAmount != null ? record.originalRefundAmount : "",
                        record.originalEstimatedAmount != null ? record.originalEstimatedAmount : "",
                        record.originalPaidAmount != null ? record.originalPaidAmount : "",
                        record.newLogisticsStatus != null ? record.newLogisticsStatus : "",
                        record.newLogisticsStatusName != null ? record.newLogisticsStatusName.replace(",", "，") : "",
                        countRouteNodes(record.newRouteData),
                        record.newActualWeight != null ? record.newActualWeight : "",
                        record.newCollectTime != null ? record.newCollectTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "",
                        record.newReceiverProvince != null ? record.newReceiverProvince.replace(",", "，") : "",
                        record.newProductMarketId != null ? record.newProductMarketId : "",
                        record.newSupplementAmount != null ? record.newSupplementAmount : "",
                        record.newRefundAmount != null ? record.newRefundAmount : "",
                        record.calculatedReceivableAmount != null ? record.calculatedReceivableAmount : "",
                        record.calculatedPayableAmount != null ? record.calculatedPayableAmount : "",
                        record.amountDifference != null ? record.amountDifference : "",
                        record.walletOperationType != null ? record.walletOperationType : "",
                        record.walletTransactionNo != null ? record.walletTransactionNo : "",
                        record.walletBalanceBefore != null ? record.walletBalanceBefore : "",
                        record.walletBalanceAfter != null ? record.walletBalanceAfter : "",
                        record.success ? "是" : "否",
                        record.errorMessage != null ? record.errorMessage.replace(",", "，") : "",
                        record.updateType.getDescription(),
                        record.isPreview ? "预览" : "真实",
                        record.processTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                        record.processDuration,
                        record.threadName != null ? record.threadName : ""
                ));
                writer.newLine();
            }

            log.info("📄 批量更新记录已保存到文件：{}", fileName);

        } catch (IOException e) {
            log.error("❌ 保存批量更新记录文件失败：{}", e.getMessage());
        }
    }

    /**
     * 保存省份提取记录到CSV文件 - 专门用于省份对比
     */
    private void saveProvinceExtractRecords(BatchUpdateStatistics statistics, boolean isPreview) {
        String operationType = isPreview ? "preview" : "real";
        String fileName = String.format("province_extract_%s_%s.csv",
                operationType, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(fileName), "UTF-8"))) {

            // 写入CSV头部 - 专门用于省份对比
            writer.write("运单号,原始地址,原省份,新省份,是否成功,错误信息,处理时间,处理耗时");
            writer.newLine();

            // 写入数据
            for (WaybillUpdateRecord record : statistics.records) {
                writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s",
                        record.waybillNo != null ? record.waybillNo : "",
                        record.originalReceiverAddress != null ? record.originalReceiverAddress.replace(",", "，") : "",
                        record.originalReceiverProvince != null ? record.originalReceiverProvince.replace(",", "，") : "",
                        record.newReceiverProvince != null ? record.newReceiverProvince.replace(",", "，") : "",
                        record.success ? "成功" : "失败",
                        record.errorMessage != null ? record.errorMessage.replace(",", "，") : "",
                        record.processTime != null ? record.processTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "",
                        record.processDuration
                ));
                writer.newLine();
            }

            log.info("📄 省份提取记录已保存到文件：{}", fileName);

        } catch (IOException e) {
            log.error("❌ 保存省份提取记录文件失败：{}", e.getMessage());
        }
    }
}
